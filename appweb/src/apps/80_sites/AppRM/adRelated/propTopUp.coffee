config = CONFIG(['stripe','share','serverBase','contact'])
debug = DEBUG()
stripe_pk = config.stripe?.pubKey
stripe_sk = config.stripe?.secret
Stripe = require('stripe')(stripe_sk)
helpers = INCLUDE 'lib.helpers'
libPropertyImage = INCLUDE 'libapp.propertyImage'
sendMail = SERVICE 'sendMail'
async = require 'async'
{propPassSearchCondition,isSale} = INCLUDE 'libapp.properties'
sendMailLib = INCLUDE 'lib.sendMail'
{topUpRange,valueAmount} = INCLUDE 'libapp.topUpAndTax'
{respError} = INCLUDE 'libapp.responseHelper'
{ sprintf }  = INCLUDE 'lib.sprintf'
dataMethods = DEF 'dataMethods'
dataMethods.topUpRange = (req, user)->
  isAdmin=false
  isVip = false
  if req.isAllowed 'admin',user
    isAdmin = true
  else if req.isAllowed 'vipRealtor',user
    isVip = true
  topUpRange {isAdmin,isVip}
ProvAndCity = MODEL 'ProvAndCity'
GroupModel = MODEL 'Group'
shareHost = config.share?.host or 'https://www.realmaster.com'

# User = COLLECTION 'chome', 'user'
Bookkeeping = COLLECTION 'chome', 'bookkeeping'
Billing = COLLECTION 'chome', 'billing'
Properties = MODEL 'Properties'
ProvAndCity = MODEL 'ProvAndCity'

UserModel = MODEL 'User'
removePopCityCacheByProvAndCity = DEF 'removePopCityCacheByProvAndCity'
checkVersionNShow = DEF 'checkVersionNShow'
generateEmailM = DEF 'generateEmailM'
# MAP_PROP_FIELDS = DEF 'MAP_PROP_FIELDS'
# ALL_FIELDS =  Object.assign libproperties.ALL_FIELDS,{topMt:1}

getConfig = DEF 'getConfig'
MSG_STRINGS.def {
  ERROR_PRICE: 'Incorrect charege amount',
}

allTopListings = []
allTopListingsRM = []
userkeyValue = {}
toppedByAgent = (p, user)->
  if /^RM/.test p.id
    return true
  return user?.roles?.indexOf('realtor') > -1
getTopUpUsers = (uids,{topTs},cb)->
  return cb() if topTs and (new Date(topTs) < new Date())
  return cb() unless Array.isArray uids
  return cb() if uids and (uids.length is 0)
  UserModel.getListByIds uids,{projection:UserModel.BASIC_INFO_FIELDS},(err,users)->
    return cb(err,null) if err
    cb(null,users)
DEF 'getTopUpUsers',getTopUpUsers

getPropAdrltr = (p)->
  uid = getTopUid(p)
  if uid and toppedByAgent(p,userkeyValue[uid])
    #if MLS and topagent not realtor
    return userkeyValue[uid]
  null
DEF 'getPropAdrltr',getPropAdrltr

initTopListings = ({src},cb)->
  cb ?= (err)->
    debug.error err if err
  Properties.findAllTopListing {src},(err,ret)->
    topListings = ret or []
    if src is 'RM'
      allTopListingsRM = topListings
    else
      allTopListings = topListings
    uids = []
    for p in topListings
      if p.hideInfo is true
        continue
      if topuid = getTopUid p
        if uids.indexOf topuid is -1
          uids.push topuid
    getTopUpUsers uids,{},(err,users)->
      return cb(err) if err
      return cb() unless users
      for u in users
        u.avt = libPropertyImage.replaceRM2REImagePath u.avt if u.avt
        userkeyValue[u._id] = u
      for p in topListings
        if p.hideInfo is true
          continue
        p.adrltr = getPropAdrltr(p)
      cb() if cb
DEF 'initTopListings',initTopListings

getTopUid = (p) ->
  if p and p.topuids and p.topuids.length > 0
    lastIdx = p.topuids.length - 1
    return p.topuids[lastIdx]
  return null
DEF 'getTopUid',getTopUid

# 0 = 0 chance
# 1 = 50% > 1
# 2 = 67% > 1
# 3 = 75% > 1
getPropWeight = (p)->
  return 1 unless p.topMt
  compareDays = getConfig('topListingCmpDys') or 3
  oneDay = 1000*3600*24
  now = new Date().getTime()
  diff = Math.floor(Math.abs(p.topMt.getTime() - now)/oneDay)
  # Math.rand()*(x+1)-1 > 0
  if diff <= 1
    return 1.6 #60%
  else if diff <= compareDays
    return 1.2
  1 # smaller will get smaller index so that have more chance to show on front

getNextTopListings = (params) ->
  selectedTopListings = []
  topListings = if params?.src is 'RM' then allTopListingsRM else allTopListings
  return [] if topListings.length is 0
  for p in topListings
    delete p.login #reset p.login
    if propPassSearchCondition p, params
      p.topWeight = getPropWeight p
      selectedTopListings.push p
    # else
    #   console.log '++++',p,fields
  numAll = selectedTopListings.length
  topListingLimit = getConfig('topListingLimit') or 5
  # selectedTopListings = selectedTopListings.slice(0,topListingLimit)
  if numAll > topListingLimit
    selectedTopListings = helpers.shuffleField(selectedTopListings,'topWeight').slice(0,topListingLimit)
  else
    selectedTopListings = helpers.shuffle(selectedTopListings)
  return selectedTopListings or []
  # if numAll > topListingLimit
  #   if startIndex > numAll
  #     startIndex = 0
  #   endIndex = startIndex + topListingLimit
  #   if endIndex > numAll
  #     # if next endindex > number of all toplisting
  #     # get end part
  #     endPart = selectedTopListings.slice(startIndex)
  #     endIndex = topListingLimit - (numAll - startIndex)
  #     # get start part
  #     newStartPart = selectedTopListings.slice(0, endIndex)
  #     topListings = endPart.concat newStartPart
  #   else
  #     topListings = selectedTopListings.slice(startIndex,endIndex)
  #   startIndex = endIndex
  #   # maxIndex = numAll - topListingLimit
  #   # randomIndex = helpers.randomTo maxIndex
  #   # topListings = selectedTopListings.slice(randomIndex,randomIndex+topListingLimit)
  #   return topListings
DEF 'getNextTopListings', getNextTopListings

# if not is unittest
# PLSWAIT '_get_toplistings'
topListingReady = false
autoInitTopListings = () ->
  initTopListings {src:'mls'},(err) ->
    console.error err if err
    initTopListings {src:'RM'}, (err) ->
      PROVIDE '_get_toplistings' unless topListingReady
      topListingReady = true

autoInitTopListings()
setInterval autoInitTopListings, 5*60000

APP '1.5'
APP 'prop', true
APP 'topup', true

# use topTs do sort
# doReduceValues = ()->
#   q = {
#     topup_pts:{$gt:0}
#   }
#   Properties.updateMany q, {$inc:{topup_pts:-21}}, {multi:true }, (err, ret)->
#     if err
#       console.error err
#       return
#     Properties.updateMany {topup_pts:{$lte:0}}, {$unset:{topup_pts:1}}, {multi:true}, (err, ret)->
#       if err
#         console.error err
#         return
#       console.log 'Decreased topup_pts at: '+new Date()
#
# unless appConfig.satelliteMode
#   helpers.repeat 3600*1000, doReduceValues



#Get top up listings by prov and city
POST 'list', (req, resp)->
  error = (resp, err) ->
    resp.send {ok:0, err:err}
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,MSG_STRINGS.NEED_LOGIN) unless user
    # return error(resp,'Not auth') unless req.isAllowed 'devGroup'
    city = req.param 'city'
    prov = req.param 'prov'
    return error(resp,'Need City and Province') unless city and prov
    cfg = {
      city,
      prov
    }
    Properties.getTopUpListingsByCity cfg, (err, ret)->
      if err
        console.error err
        return resp.send {ok:0, err:err}
      resp.send {ok:1, props:ret}

# /1.5/prop/topup/charge
GET 'charge', (req, resp)->
  error = (resp, err) ->
    return resp.ckup 'generalError', {err_tran:req.l10n(err)}
  checkVersionNShow req,resp,'5.6.1',->
    UserModel.appAuth {req,resp}, (user)->
      return error(resp,MSG_STRINGS.NEED_LOGIN) unless user
      # return error(resp,'Not auth') unless req.isAllowed 'devGroup'
      city = req.param 'city'
      prov = req.param 'prov'
      addr = req.param 'addr'
      _id = req.param 'id'
      # need to make this page to show toplisting ad and contact form
      # return error(resp,'Need Id and City') unless city and prov and addr and _id
      q = {_id:_id}
      adOnly = true unless city and prov and addr and _id
      if adOnly
        return resp.ckup 'app-charge-page', {pk:stripe_pk,adOnly:adOnly,user:user}
      Properties.findOneByID _id, {projection:{tlBcc:1,topTs:1,topuids:1,_id:1,rltr:1,saletp:1}}, (err,prop)->
        console.error err if err
        prop.tokenTopListingKey = 'topPropRent'
        if isSale(prop)
          prop.tokenTopListingKey = 'topPropSale'
        return resp.ckup 'app-charge-page', {pk:stripe_pk,adOnly:adOnly,user:user,prop:prop}


VIEW 'app-charge-page',->
  # #{@req.getProtocol()}
  js 'https://js.stripe.com/v3/'
  text """
  <script>var stripePubKey="#{@pk}";</script>
  """
  div id:'vueBody',->
    text """<app-top-up-pay></app-top-up-pay>"""
  coffeejs {vars:{
    prop:@prop,
    user:@user,
    adOnly:@adOnly,
    topup:@req.isAllowed('topup'),
    isAdmin:@req.isAllowed('topup'),
    isVipRealtor:@req.isAllowed('vipPlus')
  }}, ->
    null
  js '/libs/htmx-1.7.0.min.js'
  js '/js/entry/commons.js'
  js '/js/entry/appTopUpPay.js'
  # js 'http://checkout.stripe.com/checkout.js'

getEmail = (eml)->
  if Array.isArray eml then eml[0] else eml

generateBillingObject = (req, cfg)->
  ret = {
    tp:cfg.tp
    item:cfg.id
    ts:new Date()
    uid:cfg.user._id
    eml:getEmail(cfg.user.eml)
    charge:cfg.charge
    originalAmount:cfg.originalAmount
    # token:cfg.token
  }
  if cfg.opuser
    ret.opuid = cfg.opuser._id
  ret

updateBilling = (req, billObj, cb)->
  if billObj.useToken
    return cb()
  db = Billing
  if req.isAllowed 'topup'
    db = Bookkeeping
  db.insertOne billObj, (err, ret)->
    cb err,ret

PRICE_PER_DAY = 30
PRICE_DAYS_MAP = {
  22600:5,
  33900:10,
  45200:15,
  56500:20
  # 16950:15,
  # 28250:30
}
# price *100*113/100
PRICE_DAYS_MAP_VIPPLUS = {
  12204:15,
  23052:30
}
calcTopupDays = (amount,isVipPlus)->
  day = 0
  if isVipPlus
    if day = PRICE_DAYS_MAP_VIPPLUS[amount]
      return day
  else
    if day = PRICE_DAYS_MAP[amount]
      return day
  return (amount/100)/PRICE_PER_DAY



notifyRelatedUser = (cfg, cb)->
  notifyArray = ['<EMAIL>']#<EMAIL>
  if config.serverBase.developer_mode
    notifyArray = []
  user = cfg.user
  token = cfg.token or {}
  charge = cfg.charge
  req = cfg.req
  bill = cfg.bill
  newTs = cfg.prop?.newTs
  eml = if Array.isArray user.eml then user.eml[0] else user.eml
  notifyArray.push eml
  if token.email
    notifyArray.push token.email if token.email isnt eml
  # console.log notifyArray
  unless newTs
    isVipPlus = user.roles.indexOf('vip_plus') > 0
    # days 是从前端传过来的，没有的情况下需要计算
    if cfg.days
      days = cfg.days
    else
      days = calcTopupDays(parseInt(charge.amount),isVipPlus)
    now = Date().now()
    newTs = new Date(+now+(days*24*3600))
  ## set transaction email start day +1
  startTs = cfg.prop?.topTs
  startTs = Math.max(+startTs,Date.now())
  startTs = new Date(+startTs+(24*60*60)*1000)
  body = generateEmailM(req, bill, charge, user, startTs, newTs, {sendNotice:cfg.sendNotice,prop:cfg.prop})
  # console.log body
  # return
  subject = if cfg.sendNotice then "Top Listing Notice - #{cfg.prop.sid || cfg.prop.id || cfg.prop._id} is Now Available" else 'Your Receipt from RealMaster'
  notifyArray.push cfg.tlBcc if (cfg.sendNotice and cfg.tlBcc)
  for to in notifyArray
    sendTopListingEmailToUser to, body, {subject:subject}
  if cb
    return cb null,msg

sendTopListingEmailToUser = (to, body, opt={})->
  errs = []
  engine = opt.engine or 'SES' # 'gmail'
  mail =
    _trans: 'smtp'
    engine: engine
    from: sendMail.getFromEmailByEngine(engine)
    replyTo: opt.replyTo or "RealMaster Technology Inc.<#{config.contact?.defaultRTEmail}>"
    to: to
    subject: opt.subject or 'New Message From RealMaster APP'
    text: body
    html: body
    isNeedValidate: true
    eventType: 'TopListingEmail'
  # console.log 'mail: ->',{from:mail.from,to:mail.to,sub:mail.subject}
  # return
  sendMail.sendMail mail,(err,response)->
    if err
      console.error err
      errs.push err

handleChargeRequest = ({req,resp,user,useToken},cb)->
  error = (err) ->
    cb {ok:0, err:err}
  body = req.body ?= {}
  hasValue = body.chargeAmount and body.originalAmount and body.days
  return error(MSG_STRINGS.BAD_PARAMETER) unless hasValue
  # UserModel.appAuth {req,resp}, (user)->
  return error(MSG_STRINGS.NEED_LOGIN) unless user
  # return error(resp,'Not auth') unless req.isAllowed 'devGroup'
  return error(MSG_STRINGS.MSG_STRINGS) unless id = req.param 'id'
  # return error(resp,'Not Auth User') unless req.isAllowed 'topup'
  amount = parseInt body.chargeAmount
  originalAmount = parseInt body.originalAmount
  days = parseInt body.days
  return error(MSG_STRINGS.BAD_PARAMETER) if (days < 1) or (days > 61)
  # days = calcTopupDays(amount,vip)
  # TODO: compare days?
  tlBcc = body.tlBcc
  prov = ProvAndCity.getProvAbbrName body.prov if body.prov
  cnty = ProvAndCity.getCntyAbbrName body.cnty if body.cnty
  userIsAdmin = req.isAllowed 'topup',user
  userIsVip = req.isAllowed 'vipRealtor',user
  unless useToken
    amount = valueAmount {userIsAdmin,userIsVip,prov,cnty,\
        selectedRealtorIsVip:body.topuprole,days,originalAmount,chargeAmount:amount}
  if (not amount) or (amount < 1)
    return error(MSG_STRINGS.ERROR_PRICE)
  uid = user._id
  topupuid = body.topupuid
  sendReceipt = body.sendReceipt
  hideInfo = body.hideInfo
  if (sendReceipt is null) or (sendReceipt is undefined)
    sendReceipt = true
  if topupuid
    opuser = user
    uid = topupuid
    # findonewith opt
  UserModel.findById uid,{},(err, user)->
    debug.error err if err
    return error(MSG_STRINGS.USER_NOT_FOUND) unless user
    token = {}
    if allowedTopup = req.isAllowed 'topup' or useToken
      uid = user?._id or req?.user?._id
      charge = {amount_total:amount}
      token.token = new Date()
    else
      # TODO: check url topRandKey == session topRandKey
      charge = req.session?.get 'paymentSession'
      topRandKey = req.session?.get 'topRandKey'
      req.session.set 'paymentSession',null
      req.session.set 'topRandKey',null
      if topRandKey isnt body.encrypt
        debug.error 'topRandKey does not match'
        return error(MSG_STRINGS.BAD_PARAMETER)
    bill = generateBillingObject req, {
      originalAmount
      charge,
      currency:'cad',
      user,
      opuser,
      id,
      tp:'PropertyTopUp'
    }
    # vip = req.isAllowed('vipPlus',user)
    Properties.doTopListing { id, amount, uid, days, tlBcc, hideInfo }, (err, prop) ->
      if err
        debug.error err
        return error(MSG_STRINGS.DB_ERROR)
      removePopCityCacheByProvAndCity(prop.prov, prop.city)
      if useToken
        bill.useToken = true
      updateBilling req, bill, (err, ret)->
        if err
          debug.error err
          return error(MSG_STRINGS.DB_ERROR)
        if sendReceipt
          notifyRelatedUser {req, user, token, charge, bill, prop, days}
        if (sendNotice = body.sendNotice) and allowedTopup
          notifyRelatedUser {req, user, token, charge, bill, prop, days, sendNotice,tlBcc}
        initTopListings {src:prop.src}, (err) ->
          console.error err if err
          return resp.send {ok:1, msg:req.l10n('Payment successful'),sendEmailAddress:UserModel.getEmail user}
        return null
      # )
      

# admin topup, need to verify caller
# /1.5/prop/topup/charge
POST 'charge', (req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    # 之前是只有topup权限才能使用，现在增加房大师经纪特定房源可以使用token兑换置顶
    if not req.isAllowed 'topup'
      try
        isGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
      catch err
        debug.error err
        return respError {category:MSG_STRINGS.DB_ERROR, resp}
      unless isGroup
        return respError {category:MSG_STRINGS.NO_AUTHORIZED, resp}
    param = {req,resp,user}
    rltr = req.body?.rltr # 存在这个变量表示使用token兑换置顶
    if rltr and /avion realty inc/.test(rltr)
      param.useToken = true
    handleChargeRequest param,(err,ret)->
      return resp.send err if err
      resp.send ret
  
parseUrl2Obj = (str) ->
  str ?= ''
  str = decodeURIComponent str
  if /\?/.test str
    str = str.slice(1)
  else
    str = str
  strs = str.split('&')
  body = {}
  for i in strs
    if i
      body[i.split('=')[0]]=i.split('=')[1]
  return body

GET 'createSession', (req, resp)->
  error = (resp, err) ->
    return resp.ckup 'generalError', {err_tran:req.l10n(err)}
  checkVersionNShow req,resp,'5.6.1',->
    # console.log str
    body = parseUrl2Obj req.search
    # console.log body
    UserModel.appAuth {req,resp}, (user)->
      return error(resp,MSG_STRINGS.NEED_LOGIN) unless user
      # return error(resp,'Not auth') unless req.isAllowed 'topup'
      return resp.ckup 'charge/createSession', {body,stripePubKey:stripe_pk,\
        jsonBody:helpers.encodeObj2JsonString(body)}

POST 'createSession',(req,resp)->
  error = (resp, err) ->
    resp.send {ok:0, err:err}
  UserModel.appAuth {req,resp}, (user)->
    # TODO: use MSG_STRINGS
    return error(resp,MSG_STRINGS.NEED_LOGIN) unless user
    # return error(resp,'Not auth') unless req.isAllowed 'devGroup'
    body = req.body ?= {}
    return error(resp,MSG_STRINGS.BAD_PARAMETER) unless id = body.id
    amount = parseInt body.chargeAmount
    originalAmount = parseInt body.originalAmount
    days = parseInt body.days
    return error(resp,MSG_STRINGS.BAD_PARAMETER) if (days < 1) or (days > 31)
    # days = calcTopupDays(amount,vip)
    # TODO: compare days?
    tlBcc = body.tlBcc
    prov = ProvAndCity.getProvAbbrName body.prov if body.prov
    cnty = ProvAndCity.getCntyAbbrName body.cnty if body.cnty
    userIsAdmin = req.isAllowed 'admin',user
    userIsVip = req.isAllowed 'vipRealtor',user
    amount = valueAmount {userIsAdmin,userIsVip,prov,cnty,\
        selectedRealtorIsVip:body.topuprole,days,originalAmount,chargeAmount:amount}
    if (not amount) or (amount < 1)
      return error(resp,MSG_STRINGS.ERROR_PRICE)
    data = ''
    # data =  JSON.stringify body#encodeURIComponent
    for k,v of body
      data += "#{k}=#{v}&"
    topRandKey = helpers.randomString()
    data += 'encrypt='+topRandKey
    # 需要app链接的domin
    host = req.headers.origin
    try
      session = await Stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [{
          price_data: {
            product_data: {
              name: body.addr,
              description: 'Please do not close this window, when the payment is in processing.',
            },
            unit_amount: amount,
            currency: 'cad',
          },
          quantity: 1,
        }],
        mode: 'payment',
        success_url: "#{host}/1.5/prop/topup/afterPay?#{data}",
        cancel_url: "#{host}/1.5/prop/topup/afterPay?cancel=1"
      })
      req.session?.set 'paymentSession',session
      req.session?.set 'topRandKey',topRandKey
      return resp.send {ok:1, session}
    catch e
      console.error  e
      return error(resp,'Session Error')

# NOTE: user closed this page after stripe success causes bug not postign to server
# TODO: afterPay error handleing when stripe pay fails or not showing
GET 'afterPay', (req, resp)->
  error = (resp, err) ->
    return resp.ckup 'generalError', {err_tran:req.l10n(err)}
  checkVersionNShow req,resp,'5.6.1',->
    body = parseUrl2Obj req.search
    if body.cancel
      return resp.ckup 'charge/succeed', {body:{cancel:1},msg:req.l10n('Canceled'),instructions:''}
    UserModel.appAuth {req,resp}, (user)->
      unless user
        debug.error 'User not loggedin access afterPay'
        return error(resp,MSG_STRINGS.NEED_LOGIN)
      uid = user._id
      if topupuid = body.topupuid
        uid = topupuid
      req.body = body #should be a empty body from get
      handleChargeRequest {req,resp,user}, (err,ret)->
        if err
          return error(resp, err.err)
        # TODO: BUG: this findbyid is duplicate of handleChargeRequest
        UserModel.findById uid,{},(err, user)->
          if err
            debug.error err
            return error(resp, MSG_STRINGS.DB_ERROR)
          # return error(resp,'Not auth') unless req.isAllowed 'devGroup'
          instructions = sprintf req.l10n('We will send a receipt shortly to %s. \
            Top listing will take effect within 1 hour after your payment.'),UserModel.getEmail user
          # TODO: topup/charge here then render success
          return resp.ckup 'charge/succeed', {body,msg:req.l10n('Payment successful'),instructions}



APP '1.5'
APP 'prop', true
APP 'rmtopup', true

# GET 'charge', (req, resp)->
#   error = (resp, err) ->
#     return resp.ckup 'generalError', {err:req.l10n(err)}
#   checkVersionNShow req,resp,5.0001,->
#     UserModel.appAuth {req,resp}, (user)->
#       return error(resp,'Need login') unless user
#       return error(resp,'No auth') unless req.isAllowed 'topup'
#       city = req.param 'city'
#       prov = req.param 'prov'
#       addr = req.param 'addr'
#       _id = req.param 'id'
#       return error(resp,'Need Id and City') unless city and prov and addr and _id
#       return resp.ckup 'app-rmcharge-page', {}


# VIEW 'app-rmcharge-page',->
#   # text """
#   # <script>var stripePubKey="#{@pk}";</script>
#   # """
#   div id:'vueBody',->
#     text """<app-r-m-top-up-pay></app-r-m-top-up-pay>"""
#   coffeejs {vars:{
#     topup:@req.isAllowed('topup'),
#     }}, ->
#     null
#   js '/js/entry/commons.js'
#   js '/js/entry/appRMTopUpPay.js'
  # js 'http://checkout.stripe.com/checkout.js'

#stripe callback, need to verify caller
# POST 'charge', (req, resp)->
#   return resp.send {e:'Not supported', ok:0}
#   error = (resp, err) ->
#     resp.send {ok:0, err:err}
#   UserModel.appAuth {req,resp}, (user)->
#     return error(resp,'Need login') unless user
#     # return error(resp,'Not auth') unless req.isAllowed 'devGroup'
#     return error(resp,'Need id') unless id = req.param 'id'
#     return error(resp,'Not Auth User') unless req.isAllowed 'topup'
#     req.body ?= {}
#     amount = parseInt req.body.chargeAmount
#     if amount < 3000
#       return resp.send {ok:'0', err:'At least $30'}
#     charge = {amount:amount}
#     token = {}
#     email = if Array.isArray user.eml then user.eml[0] else user.eml
#     #TODO use uid
#     vip = req.param 'isVipClient'
#     days = parseInt req.param('days')
#     if days > 30 or days < 1
#       days = 0
#     if ueml = req.param 'ueml'
#       token.email = ueml
#       email = ueml
#     UserModel.findByEml email,{},(err,retu)->
#       if err
#         return error(resp,err.toString())
#       unless retu
#         console.log "MSG: User:#{email} not found, use operators email."
#       else
#         user = retu
#       bill = generateBillingObject req, {
#         charge:amount,
#         currency:'cad',
#         user,
#         id,
#         tp:'PropertyTopUp'
#       }
#       doTopListing {days,vip, db:RMProperties,id:id, amount, user}, (err, prop)->
#         if err
#           console.error err
#           return resp.send {ok:0, err:err.toString()}
#         updateBilling req, bill, (err, ret)->
#           if err
#             console.error err
#             return resp.send {ok:0, err:err.toString()}
#           notifyRelatedUser {req, user, token, charge, bill, prop}
#           prop.topTs = prop.newTs or prop.topTs
#           initTopListings (err)->
#             console.error err if err
#             return resp.send {ok:1, msg:req.l10n('Payment successful')}
#     null
