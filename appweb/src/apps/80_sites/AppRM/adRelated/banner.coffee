statHelper = INCLUDE 'libapp.stat_helper'
helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'
{ replaceRM2REImagePath, removeHostFromUrl, uploadPathRegex } = INCLUDE 'libapp.propertyImage'
ProjectModel = MODEL 'Project'
Ads = COLLECTION 'chome','ads'
ProvAndCity = MODEL 'ProvAndCity'
UserModel = MODEL 'User'
AdvertisementModel = MODEL 'Advertisement'
AdvertisementNewModel = MODEL 'AdvertisementNew'
{ FIELDS_BY_TYPE } = INCLUDE 'libapp.advertisement'
defaultCityToronto = DEF 'defaultCityToronto'
debug = DEBUG()
{respError} = INCLUDE 'libapp.responseHelper'


gBanners = {}
gBannerList = []
gBannerByIDs = {}

bannerFields = {_id:1, p:1, src:1, lang:1, impImgUrl:1, inapp:1, \
  tgt:1, end:1, city:1, name:1, name_en:1, builder:1, desc:1, desc_en:1, desc1:1, desc2:1,\
  linkText:1, doStr:1, ignoreStr:1, canNSA:1, persistent:1, newVer:1,\
  custtip:1, cOnly:1, rOnly:1, cipOnly:1, url:1, loc:1, nm:1, tl:1, \
  cAndR:1, st:1, order:1, prov:1, ts:1, pid:1}
getUserCity = (req, user, cities, idx)->
  idx ?= 0
  if (not cities) or (not cities?.length)
    return user.city if user?.city?.city #check if user.city is {}
    return req.session.get('city') or defaultCityToronto
  return cities[idx] or defaultCityToronto
DEF 'getUserCity',getUserCity

isClient = (req)->
  if req.hasRole('realtor')
    return false
  # NOTE: legacy business logic, dont show (realtor related)ad to followed client, caused bug report from sales
  # if req.session?.get('user')?.flwng?.length
  #   return false
  true

filterBanners = (req, user={}, l, vcinc,mode)->
  l ?= []
  ret = []
  userCity = getUserCity(req,user,null)
  userProv = ProvAndCity.getProvAbbrName userCity.prov #abbrFormat
  for b in l
    #china ip only ads
    if b.prov
      #b.prov should always be array of prov abbr version
      if (userProv not in b.prov)
        continue
    # if b.cities?.length and (userCity.city not in b.cities)
    #   continue

    if b.grp is 'realtor' and not req.hasRole('realtor')
      continue
    else if b.grp is 'notRealtor' and req.hasRole('realtor')
      continue
    else if b.grp is 'noFollow' and user.flwng
      continue

    isCip = req.isChinaIP()
    if b.area?.length < 2
      if b.area.indexOf('china') > -1 and not isCip
        continue
      if b.area.indexOf('nochina') > -1 and isCip
        continue

    if mode and not b[mode]
      continue

    ret.push b
  if vcinc and ret.length
    for b in ret
      b.vinc++
  ret
DEF "filterBanners", filterBanners

injectProjectUserAvts = (list,cb)->
  spusers = []
  projIdMap = {}
  # userEmlMap = {}
  userIdMap = {}
  ProjectModel.findAds list,(err,pList)->
    if err
      return cb err
    unless pList.length
      return cb null,list
    for p in pList
      # if p.spuser?.length
      #   spusers = spusers.concat(p.spuser)
      if p.spuids?.length
        spusers = spusers.concat(p.spuids)
    # TODO:db.getCollection('pre_constrct').find({spuids:{$exists:false},spuser:{$exists:1}})
    # has no spuid
    # orCond = [{eml:$in:spusers}, {_id:$in:spusers}]
    # console.log '++++++',JSON.stringify(spusers)
    # {$or:orCond}
    projection = {locale:1,nm:1,nm_zh:1,nm_en:1,avt:1,eml:1}
    UserModel.getListByIds spusers,{projection},(err,uList)->
      # console.log '_______',uList
      unless uList?.length
        return cb null,list
      for u in uList
        # eml = if Array.isArray u.eml then u.eml[0] else u.eml
        # userEmlMap[eml] = u
        userIdMap[u._id] = u
      for p in pList
        if p.spuids?.length
          uid = p.spuids[0]
          user = userIdMap[uid]
          if avt = user?.avt
            p.avt = replaceRM2REImagePath avt
          p.nm = libUser.fullNameOrNickname(user.locale, user)
          projIdMap[p._id.toString()] = p
        # if p.spuser?.length
        #   ueml = p.spuser[0]
        #   if avt = userEmlMap[ueml]?.avt
        #     p.avt = avt
        #     p.nm = userEmlMap[ueml]?.nm
        #   projIdMap[p._id.toString()] = p
      for ad in list
        if ad.p is 'project'
          if p = projIdMap[ad.pid]
            # console.log '++++@'
            ad.proj_city ?= p.city
            ad.proj_prov ?= p.prov
            ad.avt = p.avt
            ad.emls = p.spuser
            ad.spuids = p.spuids
            ad.adrltr = {avt:p.avt,nm:p.nm}
          # console.log ad
      cb null,list
    # for ad in list
    #   if projIdMap[ad.pid]

reloadADs = (cb)->
  try
    now = new Date()
    activeAds = await AdvertisementNewModel.findWithCounts 'A'
    gBanners = {}
    gBannerByIDs = {}
    injectProjectUserAvts activeAds, (err,injectedAds)->
      throw err if err
      for ad in injectedAds
        ad._id = ad._id.toString() if ad._id
        ad.vinc = 0
        ad.cinc = 0
        ad.cipc = 0
        ad.showc = 0
        ad.posn = {}
        if (ad.type is 'project') or uploadPathRegex.test ad.src
          ad.src = replaceRM2REImagePath ad.src
        if not ad.end
          ad.noend = true
        else if ad.end? and (now < ad.end)
          ad.expd = helpers.dayDiff(now, ad.end)
        else if ad.type is 'event'
          ad.expd = -1
        else
          continue # expired ad, event st is A, pass it
        for l in ad.lang
          bl = (gBanners[l] ?= {})
          (bl[ad.type] ?= []).push ad
        gBannerByIDs[ad._id?.toString()] = ad
      gBannerList = injectedAds
  catch error
    debug.error error
  finally
    cb() if cb
DEF 'reloadBannerADs',reloadADs

countDown = 10
# start load ADs when server start
reloadADs()
helpers.repeat 60000, ->
  try
    await AdvertisementNewModel.updateADCounter gBannerList
  catch err
    console.error err
  if countDown-- < 0
    countDown = 10
    reloadADs()

getBanners = (lang,tp,noCount)->
  return null unless l = gBanners[lang]
  if ret = l[tp]
    unless noCount
      for ad in ret
        ad.vinc++
  ret
DEF 'getBanners',getBanners

dataMethods = DEF 'dataMethods'
dataMethods.realtorPageAd = (req, user, cb)->
  ret = getBanners(req.locale(),'realtor',false) or {}
  cb null,ret

bannerClicked = ({id,isCip,posn})->
  if url = (ad = gBannerByIDs[id])?.tgt
    ad.cinc++
    if isCip
      ad.cipc++
    if posn
      ad.posn[posn+'cc'] ?= 0
      ad.posn[posn+'cc']++
    return ad
  null
DEF 'bannerClicked',bannerClicked
bannerOpened = (id)->
  if ad = gBannerByIDs[id]
    ad.showc++
    return ad
  null

APP 'adJump'
GET ':id',(req, resp) ->
  err = 'Inactive or Unknown ADs'
  id = req.param 'id'
  utmSource = req.param 'utm_source'
  if not (id and ('string' is typeof id))
    return resp.ckup 'generalError', {err_tran:req.l10n(err)}
  posn = null
  if utmSource
    posn = req.param 'posn'
  url = bannerClicked({id,isCip:req.isChinaIP(),posn})?.tgt
  if utmSource
    try
      detail = await AdvertisementNewModel.findById id,{projection:{tgt:1}}
    catch error
      debug.error error
      return resp.ckup 'generalError', {err_tran:req.l10n(err)}
    url = detail?.tgt
    if /new-condos/.test url
      url += '&rmsrc=edmad'
  if newUrl = req.param 'url'
    url = decodeURIComponent newUrl
  if not url
    err = 'This ad has no URL'
    return resp.ckup 'generalError', {err_tran:req.l10n(err)}
  resp.redirect url

APP 'utm_stat'
GET ':id',(req, resp) ->
  id = req.param 'id'
  bannerOpened id
  resp.send ''


APP '1.5'
APP 'banner', true
APP 'manage', true

GET '', (req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return resp.redirect '/1.5/index' unless req.isAllowed 'marketAdmin'
    resp.ckup 'banner-manage'

# getDetialedStats     '/1.5/banner/manage/stats'
POST 'stats',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless set = req.body
    {type,id,isNew} = set
    if not AdvertisementNewModel.checkRoles {req,type,user}
      return resp.ckup 'generalError',{err:MSG_STRINGS.NO_AUTHORIZED}
    model = AdvertisementModel
    if isNew
      model = AdvertisementNewModel
    # reloadADs ()->
    try
      ad = await model.getADCountsById id
      if uploadPathRegex.test ad.src
        ad.src = replaceRM2REImagePath(ad.src)
      return resp.send {ok:1, ad}
    catch error
      debug.error error
      return resp.send {ok:0}


POST 'bannerList', (req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return resp.send {ok:0, err: 'Need login'} unless user
    return resp.send {ok:0, err: 'Not auth'} unless req.isAllowed 'marketAdmin'
    model = AdvertisementModel
    activeAds = []
    if req.param 'new'
      model = AdvertisementNewModel
      # activeAds = gBannerList?.slice() or []
    reloadADs ()->
    try
      monthAgoTs = statHelper.getLastMonth(new Date(),3)
      activeAds = await model.findWithCounts 'A'
      inactiveAds = await model.findWithCounts 'U',monthAgoTs
      bannerWithCounts = [...activeAds, ...inactiveAds]
      bannerWithFullImageUrl = bannerWithCounts.map (banner)->
        if uploadPathRegex.test banner.src
          banner.src = replaceRM2REImagePath(banner.src)
        return banner
      return resp.send {ok:1, l:bannerWithFullImageUrl}
    catch error
      debug.error error
      return resp.send {ok:0}

LANGUAGE_LIST = DEF 'LANGUAGE_LIST'
constructLang = (req)->
  ret = []
  for i in ['zhCn'].concat LANGUAGE_LIST
    if req.param i
      if i is 'zhCn'
        ret.push 'zh-cn'
      else
        ret.push i
  ret

updateFields = [
  '_id'
  'inapp',
  'tgt',
  'p',
  'st',
  'cOnly',
  'rOnly',
  'cipOnly',
  'cAndR',
  'src',
  'noend',
  'url',
  'tl',
  'desc',
  'desc_en',
  'nm',
  'name',
  'name_en',
  'builder',
  'city',
  'linkText',
  'order',
  'prov',
  'cities',
  'rm',
  'mls'
]

POST 'update',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return resp.send {ok:0, err:'Need login'} unless user
    return resp.send {ok:0, err:'Not auth'} unless req.isAllowed 'marketAdmin'
    try
      body = req.body
      update = updateFields.reduce (acc,curr)->
        value = body[curr]
        return acc if not value?
        value = removeHostFromUrl(value) if curr is 'src'
        acc[curr] = value
        return acc
      , {}
      update.lang = constructLang(req)
      newAd = await AdvertisementModel.updateOrInsertOne {update}
      reloadADs ()->
        return resp.send {ok:1}
    catch error
      debug.error error
      return resp.send {ok:0}

VIEW 'banner-manage',->
  js '/js/Chart-3.6.2.min.js'
  div id:'vueBody',->
    text """<app-ads-manage></app-ads-manage>"""
  js '/js/entry/commons.js'
  js '/js/entry/appAdsManage.js'


# 新广告页面入口
dataMethods.adFieldsRequired = (req, user)->
  return FIELDS_BY_TYPE
APP '1.5'
APP 'ads',true

# '/1.5/ads/list'
GET 'list',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    type = AdvertisementNewModel.getAdTypesByRole req,user
    if type.length is 0
      return resp.redirect '/1.5/index'
    reloadADs ()->
    try
      # 传入权限，获取可以看到的广告
      list = await AdvertisementNewModel.getList type
    catch err
      debug.error err
      return resp.ckup 'generalError',{err:MSG_STRINGS.DB_ERROR}
    for ad in list
      if uploadPathRegex.test ad.src
        ad.src = replaceRM2REImagePath ad.src
    try
      listString = JSON.stringify(list)
      listString = encodeURIComponent listString
    catch err
      debug.error err
      return resp.ckup 'generalError',{err:MSG_STRINGS.DB_ERROR}
    # NOTE: dot ckup will interpolate \n and \'
    return resp.ckup 'advertisement/list', {listString}, '_', {noAngular:1, bootstrap:1}

# '/1.5/ads/detail/:type？id='
GET 'detail',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    id = req.param 'id'
    type = req.param 'type'
    if not AdvertisementNewModel.checkRoles {req,type,user}
      return resp.ckup 'generalError',{err:MSG_STRINGS.NO_AUTHORIZED}
    detail = {}
    if id
      try
        # 传入权限，获取可以看到的广告
        detail = await AdvertisementNewModel.findById id
      catch err
        throw err
      if uploadPathRegex.test detail.src
        detail.src = replaceRM2REImagePath detail.src
    try
      detailString = JSON.stringify(detail)
      detailString = encodeURIComponent detailString
    catch err
      debug.error err
      return resp.ckup 'generalError',{err:MSG_STRINGS.DB_ERROR}
    return resp.ckup 'advertisement/detail', {detailString}, '_', {noAngular:1, bootstrap:1}

# '/1.5/ads/detail'
POST 'detail', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless body = req.body
    if not AdvertisementNewModel.checkRoles {req,type:body.type,user}
      return respError {resp, clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED)}
    try
      id = await AdvertisementNewModel.updateOne body,user._id
    catch err
      debug.error err
      throw err
    reloadADs ()->
    return resp.send {ok:1,id}

# '/1.5/ads/pause'
POST 'pause', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        category:MSG_STRINGS.NEED_LOGIN,
        resp}
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless set = req.body
    {type,id,pause} = set
    # 权限判断
    if not AdvertisementNewModel.checkRoles {req,type,user}
      return respError {resp, clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED)}
    try
      await AdvertisementNewModel.pause {id,uid:user._id,pause}
    catch err
      debug.error err
      throw err
    reloadADs ()->
    return resp.send {ok:1}

# '/1.5/ads/splash'
POST 'splash', (req, resp) ->
  UserModel.appAuth {req,resp}, (user) ->
    ret = filterBanners(req, user, getBanners(req.locale(),'splash',true), true)
    return resp.send([]) unless ret.length
    returnList = []
    fld = ['src','tgt','inapp','lang','grp','area','st','et','prov','_id']
    for ad in ret
      a = {}
      for k in fld
        a[k] = ad[k] if ad[k]
      returnList.push a
    resp.send returnList

