path = require 'path'
fs = require 'fs'
#vueServerRenderer = require 'vue-server-renderer/build.js'
# TODO: deprecate server vue-renderer
{ createBundleRenderer } = require('vue-server-renderer')
serialize = require 'serialize-javascript'
# accessAllowed = DEF '_access_allowed'
SysData = COLLECTION "chome",'sysdata'
{formatUrlStr} = INCLUDE 'lib.helpers_string'
{getShareImg,getShareDesc} = INCLUDE 'libapp.forum'
Forum = COLLECTION 'chome','forum'
ForumModel = MODEL 'Forum'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()

config = CONFIG(['media'])
# TODO: deprecate server vue-renderer
serverBundleFilePath = path.join(config.media.path,'web/packs/bundle.webForumList.js')
serverBundleFileCode = fs.readFileSync(serverBundleFilePath, 'utf8')
#bundleRenderer = vueServerRenderer.createBundleRenderer(serverBundleFileCode);
bundleRenderer = createBundleRenderer(serverBundleFileCode)
detailserverBundleFilePath = path.join(config.media.path,'web/packs/bundle.webForumDetail.js')
detailserverBundleFileCode = fs.readFileSync(detailserverBundleFilePath, 'utf8')
#detailsBundleRenderer = vueServerRenderer.createBundleRenderer(detailserverBundleFileCode);
detailsBundleRenderer = createBundleRenderer(detailserverBundleFileCode)
getForumList = DEF 'getForumList'
getForumDetail = DEF 'getForumDetail'
webErrPage = DEF "webErrPage"
getWXConfig = DEF 'getWXConfig'

UserModel = MODEL 'User'

APP 'forum'

GET ':id/:title', (req, resp)->
  getDetails req, resp
GET ':id', (req, resp)->
  getDetails req, resp
#deprecated url
# NOTE：该接口在EDM里还在使用，未来需要使用新forum接口，暂时无法去掉
# TODO: EDM use new link https://www.realmaster.cn/1.5/topics/details?share=1&id=6822113e50d0e89b5d66c5fc&lang=zh-cn&wct=1
GET 'details/:id', (req, resp)->
  getDetails req, resp

getDetails =(req,resp) ->
  req.setLocale lang if lang = req.param 'lang'
  ctx = {webBackEnd:(req.isAllowed 'webBackEnd'), isApp: req.getDevType() is 'app', isCip: req.isChinaIP(), lang:req.locale(), isVipUser:(req.isAllowed 'vipUser'), host: req.host}
  id =req.param 'id'
  return webErrPage req, resp, 'no id', 'NA' unless id=id?.split('.')[0]

  UserModel.auth {req,resp},(user)->
    ctx.isLoggedIn = user?
    UserModel.findPublicInfo {lang:req.locale(),user},(err,sessionUser)->
      ctx.sessionUser= sessionUser
      ctx.forumAdmin = UserModel.accessAllowed 'forumAdmin',user
      req.user = user
      getForumDetail req, resp, id, (err, ret)->
        if err or !ret or  !ret.post then return webErrPage req, resp, err, 'NA'
        
        # 使用通用方法判断是否满足 .cn 域名且是个人帖子的条件
        if ForumModel.isPersonalPostInCnDomain(req.host, ret.post)
          return webErrPage req, resp, MSG_STRINGS.ACCESS_DENIED
            
        ctx.post = ret.post || []
        ctx.cmnt_userList = ret.users || []
        ctx.authorIsVip = ret.authorIsVip || false
        ctx.relatedPosts = ret.relatedPosts || []
        author  = req.l10n('Realmaster Technology Inc.')
        title = "#{ret.post.tl} | 房大师(Realmaster.com)"
        description = getShareDesc(ret.post.m)
        ctx.title = title
        ctx.isCip = req.isChinaIP()
        ctx.lang = req.locale() || 'en'
        ctx.description = description
        ctx.author = req.l10n('Realmaster Technology Inc.')
        ctx.url = "http://#{req.host}/forum/#{ret.post._id}/#{formatUrlStr ret.post.tl}"
        ctx.images = ret.post.photos||[]
        ctx.post = ret.post
        ctx.addGoogleAd = !ret.post.adTop && !ret.post.adBottom
        shareImage = getShareImg req,ret.post
        ForumModel.findRecommend {similars:ret.post.similars},(err,posts)->
          debug.error 'forumServer getDetails',err if err
          ctx.relatedPosts = posts if posts
          layoutOpt = {title,description,image:shareImage,author,page:'forumDetail'}
          unless req.isWeChat()
            return resp.ckup 'forum/detail',ctx,'layout',layoutOpt
          getWXConfig req,req.fullUrl(),(err,wxcfg)->
            layoutOpt.wxcfg = wxcfg
            resp.ckup 'forum/detail',ctx,'layout',layoutOpt

#redirect web forum list page to blog https://www.realmaster.com/blog/
GET 'list', (req, resp)->
  return resp.redirect '/blog'
  req.setLocale lang if lang = req.param 'lang'
  limit = 20 #parseInt req.param('limit') or 20
  params = {}
  params.category = req.param 'category'
  params.page = parseInt req.param('page') or 1
  params.src = req.param 'src' if req.param 'src'
  params.tag = req.param 'tag' if req.param 'tag'
  params.city = req.param 'city' if req.param 'city'
  params.prov = req.param 'prov' if req.param 'prov'
  params.gid = req.param 'gid' if req.param 'gid'
  context = {webBackEnd:(req.isAllowed 'webBackEnd'), category:params.category,currentPage: params.page, isCip: req.isChinaIP(), lang:req.locale(), forumAdmin:(req.isAllowed 'forumAdmin'),isAdmin:(req.isAllowed 'admin'), isVipUser:(req.isAllowed 'vipUser'), host: req.host}
  UserModel.auth {req,resp},(user)->
    if params.category in ['my_post','my_favourite','my_reply']
      return resp.redirect 'http://'+req.host+'/www/login' if !user
    context.isLoggedIn = user?
    SysData.findOne {_id: 'show_forum_topic'},(err,ret)->
      if err then return webErrPage req, resp, err
      getForumList  req, params, user, (err, forumList)->
        if err then return webErrPage req, resp, err
        context.forumList = forumList || []
        query = {}
        forumAdmin = UserModel.accessAllowed 'forumAdmin',user
        query = Object.assign(query, {del:{$ne:true}})  unless user and forumAdmin
        query.tp = {$exists: true, $nin:[null,'']} if params.category=='topic'
        query.uid = user?._id if params.category=='my_post'
        query.fav = {$elemMatch: {_id:user?._id}} if params.category=='my_favourite'
        query = Object.assign(query, {'cmnts.uid': user?._id}) if params.category=='my_reply'
        query.src = params.src if params.src
        query.uid = params.author if params.author
        query.tags = params.tag if params.tag
        query.tp = {$exists: true, $nin:[null,'']} if params.category=='topic'

        if params.city && params.prov
          query.city = params.city
          query.prov = params.prov

        Forum.countDocuments query, (err, count) ->
          if err then return webErrPage req, resp, err
          context.count = count
          context.totalPage = Math.ceil(count / limit)
          initialState = "<script>window.__INITIAL_STATE__=#{serialize(context, { isJSON: true })  }</script>"
          bundleRenderer.renderToString context, (err, html) ->
            if err then return webErrPage req, resp, err
            ctx = { html:html, initialState: initialState}
            ctx.title = "#{req.l10n 'Forum,Topic and Home Review'} | #{req.l10n 'Realmaster.com'}"
            ctx.description = req.l10n 'Get the Latest News, Topic and Home reviews about Real Estate & life in Canada on Realmaster.com'
            ctx.isCip = req.isChinaIP()
            ctx.author = req.l10n('Realmaster Technology Inc.')
            ctx.lang = req.locale() || 'en'
            urlbase = "http://#{req.host}/forum/list?1=1"
            ctx.url = urlbase + "#{if params.page then '&page='+params.page else ''}"
            ctx.images = ['http://'+req.host+'/web/imgs/logo2.png']
            if params.page && (params.page + 1) <= context.totalPage
              ctx.next = urlbase + "&page=#{params.page  + 1}"
            if params.page && (params.page - 1) > 0
              ctx.prev = urlbase + "&page=#{params.page  - 1}"

            resp.ckup 'forum-list',ctx,'_', {description: 1, author: 1, noref:1, noAngular:1, noAppcss:1, noRatchetcss:1, viewport:1, nosmbcss:1, noSidebarcss:1,side:0,newHeaderFooter:1,jquery:1,bootstrap:1}

VIEW 'forum-list', ->
  css '/css/apps/forum.css'
  # css '/web/css/common.css'
  text @html
  text @initialState
  js '/web/packs/commons.js'
  js '/js/lz-string.min.js'
  js '/web/packs/webForumList.js'
  # css '/web/css/bootstrap.min.css'
  # css '/css/font-awesome.min.css'
  # js '/js/jquery-2.1.1.min.js'
  # js '/web/packs/bootstrap.min.js'

VIEW 'forum-view',->
  # css '/web/css/common.css'
  if @post.wpHosts
    css '/css/apps/wpPost.css'
  css '/css/apps/forum.css'
  js '/js/rmapp.min.js'
  text @html
  text @initialState
  js '/js/lz-string.min.js'
  js '/web/packs/commons.js'
  js '/web/packs/webForumDetail.js'
  # css '/web/css/bootstrap.min.css'
  # js '/js/jquery-2.1.1.min.js'
  # js '/web/packs/bootstrap.min.js'
