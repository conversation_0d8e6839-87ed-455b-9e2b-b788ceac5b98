ObjectId = INCLUDE('lib.mongo4').ObjectId
debug = DEBUG()
objectCache = INCLUDE 'libapp.objectCache'
SysDataModel = MODEL 'SysData'
{dateFormat} = INCLUDE 'lib.helpers_date'
TokenCol = COLLECTION 'chome', 'token'
UserCol = COLLECTION 'chome', 'user'
SysDataCol = COLLECTION 'chome','sysdata'
UserProfileCol = COLLECTION 'chome', 'user_profile'
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  TokenCol.createIndex {'uid':1,'tp':1}
  TokenCol.createIndex {'uid':1,'tp':1,'ts':1}
PAGE_LIMIT = 20
SystemKey = 'systemTokenKeys'
UserProjection = {nm:1,nm_zh:1,nm_en:1,eml:1,avt:1,_id:1}
TK_CREDIT_REPORT = 'tkCreditReport'
TK_Token = 'tkToken'
CREDIT_REPORT = 'Credit Report'

# 计算新的token值是否符合规定，如果符合，返回扩大100倍的token值，和最新的token值，否则返回错误信息
# Credit Report类型的token业务逻辑：初始大家都没有值，直接记录使用情况，如果管理员给了token值，表示一种奖励。比如这个月查询报告的费用总共-30，管理员奖励10，最后问其收费20；
computeNewToken = (oldToken, token, canBeNegative) ->
  intToken100 = Number((token * 100).toFixed(0)) # 因为Token可能是2位小数，存入数据库最好是整数，所以扩大100倍
  if Number.isNaN(intToken100)
    return MSG_STRINGS.INPUT_ERROR
  if ((not oldToken) and canBeNegative)
    return intToken100
  if (not oldToken) and (intToken100 < 0)
    return MSG_STRINGS.BALANCE_BELOW_ZERO
  if (not oldToken) and (intToken100 >= 0)
    return intToken100
  newToken = parseInt(oldToken) + intToken100
  if (not canBeNegative) and (newToken < 0)
    return MSG_STRINGS.BALANCE_BELOW_ZERO
  return intToken100

# 根据给出的日期范围，获取开始时间和结束时间。结束时间为下个月的1号，查询时小于该时间就是上个月的数据
getStartTimeAndEndTime = (dateRange) ->
  startYear = dateRange[0].split('/')[0]
  startMonth = dateRange[0].split('/')[1]
  endYear = dateRange[1].split('/')[0]
  endMonth = dateRange[1].split('/')[1]
  if endMonth is '12'
    endMonth = '01'
    endYear =  Number(endYear) + 1
  else
    endMonth = Number(endMonth) + 1
  startTime = new Date("#{startYear}-#{startMonth}-01")
  endTime = new Date("#{endYear}-#{endMonth}-01 00:00:00")
  return {startTime,endTime}

# 根据type类型，获取user_profile数据库的token type key
getTokenBalanceKey = (type) ->
  tokenKey = TK_Token
  switch type
    when CREDIT_REPORT then tokenKey = TK_CREDIT_REPORT
    else TK_Token
  return tokenKey

# 根据token类型，获取修改token余额的语句
getTokenBalanceQuery = (type,token) ->
  tokenKey = getTokenBalanceKey(type)
  incQuery = {}
  incQuery[tokenKey] = token
  return incQuery

# 根据key获取缓存中的token值和类型
getSystemTokenInfoByKeys = (key) ->
  result = {goalToken: null, goalType: null}
  systemTokenCache = objectCache.getObjectCacheList 'systemTokenKeys'
  requiredToken = systemTokenCache[key]
  unless requiredToken
    return result
  result.goalToken = requiredToken.t
  result.goalType = requiredToken.tp
  return result

# 根据type获取用户余额
getUserTokenBalanceByType = (uid, type)->
  query = { _id: new ObjectId(uid)}
  option = {projection:{_id:1}}
  tokenKey = getTokenBalanceKey(type)
  option.projection[tokenKey] = 1
  tokenInfo = await UserProfileCol.findOne query,option
  unless tokenInfo
    return null
  return tokenInfo[tokenKey]
###
# @description 更新token记录和token余额
# @param {string} uid - user_id
# @param {string} type - token类型
# @param {int} token - 加减分数操作
# @param {Object} updateObj - 需要保存的数据
# @param {boolean} canBeNegative - token是否可以为负数
# @return {Object} {err, newToken} - {计算值不合理，更新后的token}
###
insertTokenRecordAndBalance = ({uid,type,token,updateObj,canBeNegative}) ->
  query = { _id: new ObjectId(uid)}
  if type?.length
    updateObj.tp = type
  currentToken = await getUserTokenBalanceByType(uid, type)
  updateToken = computeNewToken(currentToken,token,canBeNegative)
  if (updateToken is MSG_STRINGS.INPUT_ERROR) or (updateToken is MSG_STRINGS.BALANCE_BELOW_ZERO)
    return {err: updateToken, newToken: '-'}
  updateObj.t = parseInt(token * 100)
  updateObj.ts = new Date()
  updateObj.uid =  new ObjectId(uid)
  incQuery = getTokenBalanceQuery(type, updateToken)
  {transactionOptions,session} = GETTRANSACTION 'chome'
  try
    session.startTransaction(transactionOptions)
    result = await TokenCol.insertOne updateObj,{session}
    await UserProfileCol.updateOne query,{$inc: incQuery},{upsert:true,session}
    await session.commitTransaction()
  catch err
    await session.abortTransaction()
    throw new Error(err)
  finally
    await session.endSession()
  if currentToken
    newToken = currentToken + updateToken
  else
    newToken = updateToken
  return {err: null, tokenId: result.insertedId, newToken}

# 处理uids和type，获取查询数据库的query语句
dealQueryByUidsAndType = (uids,type)->
  objectUids = []
  query = {}
  if uids
    uids.forEach (uid)->
      objectUids.push(new ObjectId(uid))
    query = {uid: {$in: objectUids}}
  query.tp = { $exists: false }
  if type?.length
    query.tp = type
  return query

getSystemTokenKeysPutInMemory = () ->
  systemTokenCache = objectCache.getObjectCacheList 'systemTokenKeys'
  if systemTokenCache
    return
  systemTokenKeys = {}
  try
    result = await SysDataModel.findById(SystemKey)
  catch err
    debug.error err
    return
  unless (result?.item and Array.isArray(result.item))
    return
  for item in result.item
    systemTokenKeys[item.key] = item
  objectCache.setObjectCacheList {type:'systemTokenKeys',value:systemTokenKeys}
  return

getSystemTokenKeysPutInMemory()

class Token
  ###
  # @description 根据uid查询该用户的token历史信息
  # @param {string} uids - A necessary param
  # @param {string} type - token类型
  # @param {int} page - 页数
  # @param {striing} date - 日期，YYYY/MM
  # @return {ObjectId} {tokenList,limit} - {数据库查询结果，每次查询几个月}
  ###
  @getHistory: ({uids,type,date,page})->
    page ?= 0
    skip = if page then page * PAGE_LIMIT else 0
    option = {sort: {'ts': -1},skip,limit:PAGE_LIMIT}
    query = dealQueryByUidsAndType(uids,type)
    if date
      {endTime} = getStartTimeAndEndTime([date,date])
      query.ts = {$lt:endTime}
    tokenList = await TokenCol.findToArray query,option
    return {tokenList,limit: PAGE_LIMIT}
  
  ###
  # @description 根据uid统计查询该用户的token历史信息
  # @param {Array} uids - 查询用户的id
  # @param {string} type - token类型
  # @param {striing} date - 日期，YYYY/MM
  # @return {ObjectId} tokenList - 数据库查询结果
  ###
  @getSummaryTokenByKeyAndUid: ({uids,date,type})->
    {startTime,endTime} = getStartTimeAndEndTime([date,date])
    query = dealQueryByUidsAndType(uids,type)
    query.ts = {$gte: startTime,$lt: endTime}
    sumTokenQuery = [
      {'$match': query},
      {
        '$group': {
          _id: {key: '$key', uid: '$uid'},
          countIn:{$sum: { $cond: { if: { $gte: ["$t", 0] }, then: 1, else: 0 } }},
          countOut:{$sum: { $cond: { if: { $lt: ["$t", 0] }, then: 1, else: 0 } }},
          sumIn: { $sum: { $cond: { if: { $gte: ["$t", 0] }, then: "$t", else: 0 } } },
          sumOut: { $sum: { $cond: { if: { $lt: ["$t", 0] }, then: "$t", else: 0 } } }
        }
      },
      {'$lookup':{from: 'user',localField: '_id.uid',foreignField: '_id',as: 'user_info'}}
      { $unwind: "$user_info" },
      {
        '$project': {
          _id:1, 
          sumIn:1, 
          sumOut:1, 
          countIn:1,
          countOut:1,
          nm: "$user_info.nm",
          nm_zh: "$user_info.nm_zh" ,
          nm_en: "$user_info.nm_en",
          avt: "$user_info.avt"
        }
      },
      {'$sort':{'sumIn':-1, 'sumOut':-1}},
    ]
    tokenList = await TokenCol.aggregate sumTokenQuery
    return tokenList

  ###
  # @description 根据key统计用户的token历史信息
  # @param {string} uids - 查询用户的id
  # @param {string} type - token类型
  # @param {striing} date - 日期，YYYY/MM
  # @return {ObjectId} tokenList - 数据库查询结果
  ###
  @getSummaryTokenByKey: ({date,type,uids}) ->
    {startTime,endTime} = getStartTimeAndEndTime([date,date])
    query = dealQueryByUidsAndType(uids,type)
    query.ts = {$gte: startTime,$lt: endTime}
    sumTokenQuery = [
      {'$match': query},
      {
        '$group': {
          _id: {key: '$key'},
          countIn:{$sum: { $cond: { if: { $gte: ["$t", 0] }, then: 1, else: 0 } }},
          countOut:{$sum: { $cond: { if: { $lt: ["$t", 0] }, then: 1, else: 0 } }},
          sumIn: { $sum: { $cond: { if: { $gte: ["$t", 0] }, then: "$t", else: 0 } } },
          sumOut: { $sum: { $cond: { if: { $lt: ["$t", 0] }, then: "$t", else: 0 } } }
        }
      },
      {
        '$project': {
          _id:1, 
          sumIn:1, 
          sumOut:1, 
          countIn:1,
          countOut:1,
        }
      },
      {'$sort':{'sumIn':-1, 'sumOut':-1}},
    ]
    tokenList = await TokenCol.aggregate sumTokenQuery
    return tokenList
  
  ###
  # @description 根据给定的年月范围，聚合统计每个月的Token收支情况
  # @param {string} uids - 查询用户的id
  # @param {array} dateRange - 当前查找出的记录的最晚和最早日期
  # @param {string} type - 要统计的tokne类型
  # @return {array} tokenSumInfo - 根据年月统计收支信息
  ###
  @getSumTokenByMonth: ({dateRange,type,uids}) ->
    {startTime,endTime} = getStartTimeAndEndTime(dateRange)
    query = dealQueryByUidsAndType(uids,type)
    query.ts = {$gte: startTime,$lt: endTime}
    sumTokenQuery = [
      {'$match': query},
      {
        '$group': {
          _id: { $dateToString: { format: "%Y/%m", date: "$ts" } },
          sumIn: { $sum: { $cond: { if: { $gte: ["$t", 0] }, then: "$t", else: 0 } } },
          sumOut: { $sum: { $cond: { if: { $lt: ["$t", 0] }, then: "$t", else: 0 } } }
        }
      },
      {'$sort':{'_id':-1}},
      {'$project':{_id:1, sumIn:1, sumOut:1}}
    ]
    tokenSumInfo = await TokenCol.aggregate sumTokenQuery
    return tokenSumInfo

  ###
  # @description 将管理员给用户手动加减积分记录保存到数据库
  # @param {string} uid - 用户_id
  # @param {string} token - 加减分数操作，eg：-10，20
  # @param {string} reason - 操作理由
  # @param {string} uidAdmin - 由哪个管理员操作
  # @param {string} type - token类型
  # @return {object} {err, token} - {计算值不合理，更新后的token}
  ###
  @adminModify: ({uid, token, reason, uidAdmin, type}) ->
    updateObj =
      reason: reason
    updateObj.admUid = uidAdmin if uidAdmin
    systemTokenCache = objectCache.getObjectCacheList 'systemTokenKeys'
    canBeNegative = false # 针对type底下只有一个key的类型
    unless systemTokenCache
      return {err: MSG_STRINGS.BAD_PARAMETER, token: '-'}
    for k,v of systemTokenCache
      if v.tp is type
        canBeNegative = v.canBeNegative
        break
    {err, tokenId, newToken} = await insertTokenRecordAndBalance({uid,type,token,updateObj,canBeNegative})
    if err
      return {err, token: '-'}
    return {err: null, token: (newToken / 100).toFixed(2)}
  
  ###
  # @description 删除之前系统加减积分的记录，并且归还给用户总积分
  # @param {string} tokenId - token _id
  # @return {object} {err, token} - {计算值不合理，更新后的token}
  ###
  @cancelToken: (tokenId, needToken) ->
    unless tokenId
      return throw new Error(MSG_STRINGS.BAD_PARAMETER)
    query = {_id: new ObjectId(tokenId)}
    {transactionOptions,session} = GETTRANSACTION 'chome'
    try
      session.startTransaction(transactionOptions)
      tokenRcord = await TokenCol.findOneAndDelete query,{session}
      result = tokenRcord?.value
      unless result
        throw new Error(MSG_STRINGS.NOT_FOUND)
      goalToken = result.t * -1
      tokenKey = getTokenBalanceKey(result.tp)
      incQuery = getTokenBalanceQuery(result.tp, goalToken)
      if needToken
        resultDB = await UserProfileCol.findOneAndUpdate {_id: result.uid},{$inc: incQuery},{returnDocument:'after', session}
        newRecord = resultDB.value
        newToken = (newRecord[tokenKey] / 100).toFixed(2)
      else
        await UserProfileCol.updateOne {_id: result.uid},{$inc: incQuery},{session}
      await session.commitTransaction()
    catch err
      await session.abortTransaction()
      throw new Error(err)
    finally
      await session.endSession()
    if needToken
      return newToken
    return

  ###
  # @description 根据系统指定key来加减用户积分。如果有传入tokenId，认为是更新
  # @param {string} uid - 用户_id
  # @param {string} key - 系统指定key，eg：Credit Report
  # @param {string} memo - 显示在前端的额外信息。
  # @param {string} id - 额外信息的用于查询数据库的ID，比如房源id
  # @param {string} version - 记录报告等的版本
  # @param {int} pcOfShr - 获取到积分的百分比
  # @param {string} tokenId - token _id
  # @return {object} {err, token} - {计算值不合理，更新后的token}
  ###
  @applyToken: ({uid, key, id, memo, version, pcOfShr, tokenId}) ->
    unless uid and key
      return {err: MSG_STRINGS.BAD_PARAMETER,tokenId: null}
    if pcOfShr?
      pcOfShr = Number(pcOfShr)
      if Number.isNaN(pcOfShr) or pcOfShr < 0 or pcOfShr > 100
        return {err: MSG_STRINGS.BAD_PARAMETER}
    requiredToken = await SysDataCol.findOne {_id: SystemKey}
    unless requiredToken?.item
      return {err: MSG_STRINGS.NOT_FOUND,tokenId: null}
    index = requiredToken.item.findIndex((el)-> return el.key is key)
    if index < 0
      return {err: MSG_STRINGS.NOT_FOUND,tokenId: null}
    updateObj =
      key: key
    updateObj.id  = id if id
    updateObj.v  = version if version
    updateObj.m  = memo if memo
    goalToken = requiredToken.item[index].t
    canBeNegative = requiredToken.item[index].canBeNegative
    if pcOfShr?
      goalToken = goalToken * pcOfShr / 100
      updateObj.pcOfShr = pcOfShr
    goalType = requiredToken.item[index].tp
    {err, tokenId, newToken} = await insertTokenRecordAndBalance({uid,type:goalType,token:Number((goalToken / 100).toFixed(2)),updateObj,canBeNegative})
    if err
      return {err, tokenId: null}
    return {err:null, tokenId}

  ###
  # @description 查询所有用户的token当前分数列表
  # @param {string} eml - 用户邮箱
  # @param {int} page - 页数
  # @param {string} type - 查找的token类型
  # @return {Object} {userList, limit, userInfo} - 用户token信息列表
  ###
  @getUserList: (eml,type,page) ->
    page ?= 0
    skip = if page then page * PAGE_LIMIT else 0
    tokenKey = getTokenBalanceKey(type)
    tokenSort = {}
    tokenSort[tokenKey] = -1 # 先根据tokenKey排序
    tokenSort._id = 1
    if eml
      regexEml = new RegExp(eml, 'i')
      option = {'projection':UserProjection,sort:{_id:1},skip,limit:PAGE_LIMIT}
      userInfo = await UserCol.findToArray {eml:regexEml},option
      unless userInfo.length
        return {userList:[], limit: PAGE_LIMIT, userInfo: []}
      uidArry = []
      for user in userInfo
        uidArry.push(user._id)
      option = {projection:{_id:1},sort:tokenSort}
      option.projection.token = '$' + tokenKey
      userList = await UserProfileCol.findToArray {_id:{$in: uidArry}}, option
    else
      matchQuery = {}
      matchQuery[tokenKey] = {$exists: true}
      query = [
        {$match: matchQuery},
        {$sort: tokenSort},
        {$skip: skip },
        {$limit: PAGE_LIMIT },
        {$group: {_id: {id: '$_id', token: "$#{tokenKey}"}}},
        {
          $lookup: {
            from: "user",
            localField: "_id.id",
            foreignField: "_id",
            as: "user_info"
          }
        },
        { $unwind: "$user_info" },
        {'$project': {
          _id: "$_id.id",
          eml: "$user_info.eml",
          nm: "$user_info.nm",
          nm_zh: "$user_info.nm_zh" ,
          nm_en: "$user_info.nm_en",
          avt: "$user_info.avt"
          token: "$_id.token"
        }},
        { $sort: {token: -1, _id:1} }
      ]
      userList = await UserProfileCol.aggregate query
    return {userList, limit: PAGE_LIMIT, userInfo: userInfo or []}

  ###
  # @description 查看用户目前积分是否够扣取
  # @param {string} uid - 用户_id
  # @param {string} key - 定义的系统key
  # @return {Boolean} - 积分是否够扣取
  ###
  @canPerform: (uid, key) ->
    unless uid and key
      return false
    {goalToken, goalType} = getSystemTokenInfoByKeys(key)
    unless goalToken?
      return false
    currentToken = await getUserTokenBalanceByType(uid, goalType)
    unless currentToken
      return false
    if (currentToken + goalToken) >= 0
      return true
    return false

  ###
  # @description 查看用户想要获取的信息是否已经兑换过
  # @param {string} uid - 用户_id
  # @param {string} key - 定义的系统key
  # @param {string} id - 用于查询没有tokenID的情况下，根据id查询token记录
  # @param {boolean} onlyBalance - 是否只返回余额，不检查之前是否兑换过
  # @return {Boolean} - 是否已经兑换过，当前余额，本次操作需要扣除的积分
  ###
  @isExchanged: ({uid, key, id, onlyBalance = false}) ->
    returnInfo = {result: false, balance: '-', token: '-'}
    unless uid and key and id
      return returnInfo
    query = {uid: new ObjectId(uid),key: key, id: id}
    unless onlyBalance
      result = await TokenCol.findOne query
      if result
        returnInfo.result = true
        return returnInfo
    {goalToken, goalType} = getSystemTokenInfoByKeys(key)
    unless goalToken
      return returnInfo
    currentToken = await getUserTokenBalanceByType(uid, goalType)
    returnInfo.token = (goalToken / 100).toFixed(2)
    unless currentToken
      return returnInfo
    returnInfo.balance = (currentToken / 100).toFixed(2)
    return returnInfo
  
  ###
  # @description 查找用户所有类型的token余额
  # @param {string} uids - 需要查找的用户
  # @return {array} record - 各种类型的最新token值
  ###
  @getDiffTypeTokenBalance: (uids) ->
    unless uids?.length
      return []
    objectUids = []
    uids.forEach (uid)->
      objectUids.push(new ObjectId(uid))
    option = {projection:{_id:1}}
    option.projection[TK_CREDIT_REPORT] = 1
    option.projection[TK_Token] = 1
    record = await UserProfileCol.findToArray {_id:{$in: objectUids}}, option
    return record
    
  ###
  # @description 查询历史管理员填写的给用户修改积分原因
  # @return {array} reason - 历史原因
  ###
  @getEditReason: () ->
    reason = await TokenCol.distinct 'reason'
    return reason
MODEL 'Token', Token