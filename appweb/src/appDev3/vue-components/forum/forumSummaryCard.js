
var ForumSummaryCard = {
  mixins: [pageDataMixins,forumMixins,forumCommonMixins],
  props: {
    postType:{
      type:String,
      default:'forum'
    },
    parentPage:{
      type: String,
      default:''
    },
    post: {
      type: Object,
      default: function () {
        return {
          hasUpdate:false,
          src:'news',
        }
      }
    },
    dispVar: {
      type: Object,
      default: function () {
        return {}
      }
    },
    hideStickyIcon: {
      type:Boolean,
      default: false
    },
    noTag: {
      type: Object,
      default: function () {
        return {}
      }
    },
    noTagAction:{
      type:Boolean,
      default: false
    },
    isWeb: {
      type:Boolean,
      default: false
    },
    displayPage: {
      type:String,
      default: 'all'
    }
  },
  components: {
    LazyImage
  },
  computed:{
    computedThumb:function() {
      if(this.post.thumb) {
        return this.post.thumb
      } else if(this.post.src=='sch') {
        return '/img/school/school_forum.png'
      } else if (this.post.src=='psch') {
        return '/img/school/school_forum_p.png'
      } else {
        return null;
      }
    },
    computedForumFas: function() {
      return this.isForumFas(this.dispVar, {city:this.post.city, prov:this.post.prov});
    },
    commentsHeight: function() {
      if ( (this.post.tags && this.post.tags[0]) || this.post.src=='property')
        return 40;
      else
        return 60;
    },
    computedVc:function() {
      return (this.post.vc || 0)  + (this.post.vcc ||0)
    },
    computedTp: function () {
      if (this.post.tpbl && this.post.tpbl[0])
        return this.post.tpbl[0];
      else if (this.post.tp)
          return this.$_('Topic')
      else
        return null;
    }
  },
  data () {
    return {
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    if (!this.post.del)
      this.post.del = false;
  },
  methods: {
    toggleFlagModal(post) {
      this.$parent.toggleFlagModal(post);
      return;
    },
    parseDate(ohv=''){
      var l = ohv.split(' ');
      var p1 = l[0].split('-');
      var p2 = l[1].split('-');
      // + 'EST' 'EDT'?
      return p1[1]+'.'+p1[2]+' '+p2[0];
    },
    imageLoadError() {
      this.post.thumb = null;
    },
    openView() {
      event.stopPropagation();
      if (this.post.hasUpdate){
        this.post.hasUpdate = false;
      }
      if (this.isWeb)  {
        window.open('/1.5/forum/webedit?web=true&id='+this.post._id,'_blank');
      } else if (this.postType == 'wecard') {
        this.$parent.showWecard(this.post);
      } else {
        this.$parent.showPostView(this.post._id,this.post.src,this.post.gid);
      }
      return false;
    },
    showAd() {
      event.stopPropagation();
      this.$parent.showPostView(this.post._id,this.post.src,this.post.gid,this.post.adInlist,this.post.adTop,this.post.adTopPhoto)
    },
    addCityFilter() {
      if(this.noTagAction) {
        return;
      }
      event.stopPropagation();
      if (this.isWeb)  {
        window.open(window.location.href+'&city='+this.post.city+'&prov='+this.post.prov, '_blank')
      } else {
        this.$parent.curCity = {o:this.post.city, p:this.post.prov, cnty:this.post.cnty}
        this.$parent.reloadPosts();
      }
      return false;
    },
    openTag() {
      if(this.noTagAction) {
        return;
      }
      event.stopPropagation();
      var tag = this.post.tags[0];
      if (this.isWeb)  {
        window.open(window.location.href+'&tag='+tag, '_blank')
      } else {
        this.$parent.tag = tag;
        this.$parent.reloadPosts();
      }
      return false;
    },
    openGroup() {
      event.stopPropagation();
      if (this.isWeb)  {
        window.open(window.location.href+'&gid='+gid, '_blank')
      } else {
        this.$parent.gid = this.post.gid;
        this.$parent.gnm = this.post.gnm;
        this.$parent.reloadPosts();
      }
      return false;
    },
    openBySrcView(src) {
      if(this.noTagAction) {
        return;
      }
      event.stopPropagation();
      if (this.isWeb)  {
        window.open(window.location.href+'&src='+src, '_blank')
      } else {
        this.$parent.src = src;
        this.$parent.reloadPosts();
      }
    },
    openTp() {
      if(this.noTagAction) {
        return;
      }
      event.stopPropagation();
      if (this.post.tp) {
        if (this.isWeb)
          window.open('http://'+this.dispVar.reqHost+'/forum/'+this.post._id + '/'+ formatUrlStr(this.post.tl), '_blank')
        else
          this.$parent.showPostView(this.post._id,this.post.src);
      } else {
        var self = this;
        axios.get('/1.5/forum/findPostByTp/' + self.computedTp)
        .then((ret)=>{
          ret = ret.data
          if (ret.ok) {
            if (this.isWeb)
              window.open('http://'+self.dispVar.reqHost+'/forum/'+ret.postid, '_blank')
            else
              this.$parent.showPostView(ret.postid,this.post.src);
          } else {
              return window.bus.$emit('flash-message', ret.e);
          }
        }).catch(()=> {
          console.error(err.status+':'+err.statusText);
        });
      }
      return false;
    },
  },
  template: `
  <div class="forum-summary-card" v-bind:class="{summaryWeb: isWeb , greybg: post.gid}">
    <div class="post-top-div" style="position:relative; display:block!important;" v-if="post.adInlist && post.adTopPhoto && post.adTop">
        <div class="edit" v-if="dispVar.forumAdmin" @click="openView()"><span>{{post.vcad0}}</span><span>{{$_('Edit')}}</span></div><img class="post-top-img" v-if="post.adTopPhoto" :src="post.adTopPhoto" @click="showAd()" />
        <div class="post-top-text">{{$_('AD')}}</div>
    </div>
    <div v-else style="height: 103px;" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov }">
        <div class="post-summary">
            <div class="post-title" v-bind:class="{deleted: post.del}" @click="openView()"><span class="red-dot-forum fa fa-circle" v-if="post.hasUpdate"></span><span class="red-button" v-if="post.sticky && !noTag.top">{{$_('TOP')}}</span><span class="red-button" @click="openGroup()" v-if="post.gid && post.gnm && !noTag.gid">{{post.gnm}}</span><span class="red-button blue" @click="openTp()" v-else-if="(post.tpbl && post.tpbl.length) || (post.tp && !noTag.topic) ">{{computedTp}}</span><span class="red-button blue" @click="openTag()" v-else-if="(post.tags && post.tags[0]) && !noTag.tag"><span v-if="post.tags[0]=='HOT'">{{$_('HOT')}}</span><span v-else>{{post.tags[0]}}</span></span><span class="red-button blue" @click="openBySrcView('property')" v-else-if="post.src=='property' && post.cc>0 && !noTag.property">{{$_('Home Review')}}</span><span class="red-button blue" @click="openBySrcView('sch')" v-else-if="(post.src=='sch'||post.src=='psch') && !noTag.sch && !noTag.psch">{{$_('School Review')}}</span><span class="red-button blue" @click="addCityFilter()" v-show="(post.city||post.prov||post.cnty) && !noTag.city && post.cnty!=='No City'">{{$_(post.city||post.prov||post.cnty,'city')}}</span><span class="txt" v-if="['property','psch','sch'].indexOf(post.src)>=0&& post.cmntl">{{post.cmntl + ' ' + post.tl}}</span><span class="txt" v-else>{{post.tl}}</span></div>
            <div class="post-comments">
              <span class="post-name pull-left">{{trimStr(post.fornm,12)}}</span>
              <span class="post-bottom">
                <span v-if="post.src!='sch' && post.src!=='psch'">
                  <span v-if="dispVar.isAdmin">{{post.vc}} | {{post.vcapp}} | {{post.vcc}}</span>
                  <span v-else>{{computedVc}}</span>
                  <span class="fa fa-eye" style="padding-left:'5px'"></span>
                </span>
                <span v-if="(post.cc > 0) && (!post.discmnt || dispVar.forumAdmin)">
                  <span v-if="post.src!='sch' && post.src!=='psch'">|</span>
                  <span>{{post.cc}}</span>
                  <span class="fa fa-comments"></span>
                </span>
                <span v-if="dispVar.forumAdmin && post.similars">| S: {{post.similars.length}} |</span>
              </span>
              <span class="post-ts" v-if="dispVar.isAdmin">{{formatTs2(post.mt)}}</span>
              <span class="post-ts" style="padding-right:5px;">{{formatTs2(post.ts)}}</span>
              <span class="realtor-only" v-if="post.realtorOnly">{{$_('Realtor Only')}}</span>
              <span class="icon icon-close reportForumIcon" v-if="parentPage == 'forum'" data-sub="report forum" :data-id="post._id" @click="toggleFlagModal(post)"></span>
            </div>
        </div>
        <div style="padding-top: 5px;" @click="openView()">
            <lazy-image class="img post-img" v-if="computedThumb" :alt="post.tl" :error="imageLoadError" :src="computedThumb" :imgstyle="'width: 120px;height: 90px;background-size: 100% 100%;'"></lazy-image><span class="vidRecord" v-if="post.src == 'video' && post.vidRecord || post.passedLive" :class="{noPic:!computedThumb}"><i class="fa fa-video-camera"></i></span><span class="vidLive vidLive1" v-if="post.src == 'video' && post.vidLive && post.isLiving" :class="{noPic:!computedThumb}">{{$_('LIVE')}}</span><span class="vidLive vidLive2" v-if="post.src == 'video' && post.vidLive && !post.isLiving && !post.passedLive" :class="{noPic:!computedThumb}">{{parseDate(post.ohv)}}</span>
            <div class="img post-img" v-if="!computedThumb">
                <div class="no-img">{{post.tl? post.tl.substr(0,1) : ''}}</div>
            </div>
        </div>
    </div>
  </div>
  
  `
};