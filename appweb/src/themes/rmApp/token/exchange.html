<style>
#exchange-token{
  border: 1px solid #e5e5e5;
  padding: 10px;
}
.token-name{
  font-size: 14px;
  font-weight: bold;
  color: #333;
  padding-bottom: 10px;
}
.token-info{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.desc{
  font-size: 12px;
  color: #999;
}
.action{
  min-width: 50px;
  padding: 5px 15px;
  color: #fff;
  background: #3B7DEE;
  font-size: 14px;
  text-align: center;
  border-radius: 2px;
}
</style>
<div id="exchange-token">
  <div class="token-name">{{=- it.name}}</div>
  <div class="token-info">
    <div class="desc">
      <div>
        <span class="token-name">{{= it.token}}</span>
        <span>{{- tokens}}</span>
      </div>
      <div>{{- Balance}}: {{= it.balance}} {{- tokens}}</div>
    </div>
    <div class="action" onclick="confirmApply()">{{- Redeem}}</div>
  </div>
  <div style="display:none" id="confirmMsg">{{- Are you sure to redeem token?}}</div>
  <div style="display:none" id="confirmCancel">{{- Cancel}}</div>
  <div style="display:none" id="confirmConfirm">{{- Confirm}}</div>
  <div style="display:none" id="tokenKey">{{= it.key}}</div>
  <div style="display:none" id="schoolId">{{= it.id}}</div>
  <div style="display:none" id="memo">{{= it.memo}}</div>
  <div style="display:none" id="insufficientBalance">{{- Insufficient balance.}}</div>
  <div style="display:none" id="tokenValue">{{= it.token}}</div>
  <div style="display:none" id="balanceValue">{{= it.balance}}</div>
</div>
<script src="/libs/jquery-2.2.3.min.js"></script>
<script>
  function confirmApply(){
    if (Number($('#tokenValue').text().replace('-','')) > Number($('#balanceValue').text())) {
      RMSrv.dialogAlert($('#insufficientBalance').text());
      return;
    }
    RMSrv.dialogConfirm($('#confirmMsg').text(),
        applyToken,"",
        [$('#confirmCancel').text(), $('#confirmConfirm').text()]
      );
  }
  function applyToken(idx){
    if (idx+'' != '2') { return }
    var id = $('#schoolId').text();
    var memo = $('#memo').text();
    var key = $('#tokenKey').text();
    var url = '/token/applyToken';
    var data = {
      id: id,
      memo: memo,
      key: key
    };
    $.post(url,data,function(ret) {
      if (ret.ok == 1) {
        var noRefresh = window.exchangeNoRefresh || false;
        if (noRefresh && window.bus) {
          window.bus.$emit('token-exchange-success', {
            id: id,
            memo: memo,
            key: key,
            result: ret
          });
        } else {
          let iframe = document.querySelector("#simPopUpModal");
          if(iframe){
            iframe.contentWindow.location.reload();
          }else{
            location.reload();
          }
        }
      } else {
        RMSrv.dialogAlert(ret.e);
      }
    });

  }
</script>
