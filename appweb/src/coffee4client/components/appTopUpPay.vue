<template lang="pug">
div
  div(v-show="msg")
    //- header.bar.bar-nav
      //- a.icon.icon-close.pull-right(@click="closeTopUp()", href="javascript:void 0")
      //- h1.title {{_('Success')}}
    div.content.success
      img(src="/img/paytop/check.png")
      div.msg {{msg}}
      div.desc {{sprintf(_('We will send a receipt shortly to %s. Top listing will take effect within 1 hour after your payment.'), sendEmailAddress)}}
  div(v-show="!msg")
    page-spinner(:loading="loading")
    //- header.bar.bar-nav
      //- a.icon.icon-close.pull-right(@click="closeTopUp()", href="javascript:void 0")
      //- h1.title {{_('Promote')}}
    div.content
      div.card.desc
        img(src="/img/paytop/top.png")
        div(style="color: #e03131;font-size: 22px;",v-if="_id && (!city || !prov || !addr)") {{_('Error: Need address to topup')}}
        div(v-if="pageTp=='project'")
          div.tl {{_('Feature','top-preconstruction')}}
          div.desc {{_('Becoming a Pre-construction Exclusive Agent is an effective way to increase sales and get more potential buyers\' requests.','top-preconstruction')}}
        div(v-else)
          div.tl {{_('TOP LISTING')}}
          div.desc {{_('Your listing will be placed at the top of the listing\'s city and community category page, and has a different color marker on map view to get much better exposure.')}}
      div.signup-form(v-if="adOnly")
        div(v-if="dispVar.isRealtor")
          sign-up-form(:owner="{vip:true}",:user-form="userForm",:feedurl="feedurl",:is-web="true",:title="_('Contact Us')",:cstyle="{'padding':'10px','backgroundColor':'#fff'}")
        div.card.how-it-works.how-to-start(v-if="!dispVar.isRealtor")
          div.tl {{_('HOW TO START')}}
          div.desc
            span {{_('If you want to TOP your listing, please ask your listing agent to contact us. ')}}
            a(href="tel:9056142609") (905)614-2609
      div.card.payment(v-if="!adOnly")
        div.tl {{_('CHOOSE PACKAGE')}}
        div.addr {{addr}}
        div.topup-date {{_('Promotion expires')}}: {{newTopTs|dotdate}}
        div.desc {{_('You agree that your listing price, photos, and other relevant details to be published on RealMaster\'s website, APP and other social media platforms.')}}
        div.sendEmail.fa(:class="hideInfo?'fa-check-square-o':'fa-square-o'",@click="toggleShowInfo()") {{_('Hide top up person information')}}
        div(v-if="isAdmin")
          div.sendEmail.fa(:class="sendReceipt?'fa-check-square-o':'fa-square-o'",@click="toggleSendReceipt()") {{_('Send Receipt')}}
          div.sendEmail.fa(:class="sendNotice?'fa-check-square-o':'fa-square-o'",@click="toggleSendNotice()") {{_('Send Notice')}}
          search-user-list(:role='role',:range='true')
        div(v-if="showPaymentOpt")
          div.range-wrapper
            //- span.value {{value}}
            range.range(@on-change="onChangeVal", :value="value", :min="min", :max="max", :step="step", :minHtml="minHtml", :maxHtml="maxHtml")
          div#card-element
          div.btn-wrapper
            div.btn.btn-positive.btn-block(@click="custPurchase($event)") {{_('Pay')}} ${{value}} + {{_('Tax','sales')}}, {{days}} {{_('Days')}}
          div.desc {{_('Paying with Stripe, the third party payment system. We do not collect and save your payment infomation.')}}
        //- div.rank-in-pay!="{{_('Display spot')}}: #{{rankSpot}}"
        //-   span.pull-right(@click="showAllList()")
        //-     | {{_('All Top Listings')}}
        //-     span.fa.fa-caret-down(v-show="showAll")
        //-     span.fa.fa-caret-up(v-show="!showAll")
        //- form#payForm(action="/1.5/prop/topup/charge", method="post")
        //-   input(type="hidden" value="{{value*100}}" name="chargeAmount")
        //-   input(type="hidden" value="{{_id}}" name="id")
          //- script.stripe-button(
          //-   src="http://checkout.stripe.com/checkout.js",
          //-   data-key="pk_test_xuuOG1SkPt8l46NikwVbp39m",
          //-   data-amount="10",
          //-   data-name="name",
          //-   data-description="desc",
          //-   data-locale="auto",
          //-   data-currency="cad"
          //-   )
      div#exchange-token(v-show='showTokenBtn',ref='exchange' :hx-get="'/token/exchangeTemplate?onlyBalance=1&id='+tokenM+'&key='+tokenKey+'&memo='+tokenM+'&name=Exchange for 30 days'",target='#exchange-token',hx-trigger='click') Loading
      vip-upgrade-tip(:dispVar="dispVar",:desc="'Get extra discount for each top listing'")
      div.card.top-listings(v-show="props.length && showAll")
        //- div.tl {{_('Top Advertised listings')}}
        //- div.city {{city}}
        div.prop-wrapper
          div.prop-list(v-for="(p,index) in props", @click="showPropDetail(p)")
            div.rank {{index+1}}
            div.addr(:class="{cur:p.cur}") {{p.addr}}
            div.value Balance: {{p.topTs | time}}
            //- {{p.topup_pts/100 | currency '$' 0}}
      //- div.staging(@click="showStaging()")
      //-   img(src="/img/indexSp/staging.jpg")
      div.card.bcc(v-if="pageTp!='project'&&isAdmin")
        div.tl {{_('BCC EMAIL')}}
        div(style="padding:10px 15px;")
          input(v-model="tlBcc",style="width:calc(100% - 42px);border:1px solid #ccc")
          div.btn.btn-positive(@click="updateBcc()") {{_('Save')}}
        div(v-show="tlBccMsg",style="text-align:center;color:#5cb85c;") {{_('Updated')}}

      div.card.how-it-works(v-if="pageTp!='project'")
        div.tl {{_('HOW IT WORKS')}}
        //- div(style="height:100px;")
        img(src="/img/paytop/dollar.png")
        p.desc {{_("If there are more than 5 purchased listings in the city or community category page, they will then rotate evenly each time the page is loaded in order to ensure equal visibility.")}}
        img(src="/img/paytop/watch.png")
        p.desc {{_('Your Top Listing will take effect within 1 hour after your payment.')}}
        img(src="/img/paytop/list.png")
        p.desc {{_('We provide no guarantee regarding the number of views, replies or imporessions your ad will actually receive. We reserve the right of final interpretation of terms and conditions.')}}
</template>

<script>
import Range from './frac/Range'
import rmsrv_mixins from './rmsrv_mixins'
import pagedata_mixins from './pagedata_mixins'
import PageSpinner from './frac/PageSpinner.vue'
import SignUpForm from './frac/SignUpForm.vue'
import filters from './filters'
import VipUpgradeTip from './frac/VipUpgradeTip.vue'
import SearchUserList from './frac/SearchUserList.vue'

export default {
  filters:{
    dotdate:filters.dotdate,
  },
  mixins:[rmsrv_mixins,pagedata_mixins],
  components:{
    Range,
    PageSpinner,
    SignUpForm,
    VipUpgradeTip,
    SearchUserList
  },
  props: {
  },
  watch:{
    value (val, oldVal) {
      var valInt = parseInt(val);
      if ((this.selectedRealtor && this.selectedRealtor.isVip) || (this.isVipRealtor && !this.isAdmin)) {
        if (valInt == 108) {
          this.days = 15;
        } else if (valInt == 204) {
          this.days = 30;
        } else if (valInt == 306) {
          this.days = 45;
        } else if (valInt == 408) {
          this.days = 60;
        }
      } else {
        if (valInt == 150) {
          this.days = 15;
        } else if (valInt == 250) {
          this.days = 30;
        } else if (valInt == 375) {
          this.days = 45;
        } else if (valInt == 500) {
          this.days = 60;
        }
      }
      if (this.isAdmin) {
        this.days = this.adminRange[valInt];
      }
      this.calcTopTs();
      this.rankSpot = this.getRankFromVal(val)+1;
    }
  },
  data () {
    return {
      pageTp:vars.tp||'tl',
      tlBccMsg:false,
      tlBcc:'',
      sendReceipt:true,
      hideInfo: vars.hideInfo || false,
      role:'user',
      sendNotice:false,
      userForm:{
        ueml:'<EMAIL>',
        nm:vars.user.nm || '',
        eml:'',
        mbl:vars.user.mbl || '',
        wxid:'',
        m:'',
        tp:vars.tp == 'project'?'toppreconstruction':'toplisting',
        subject:vars.tp == 'project'?'TOP Pre-Construction Request':'TOP Listing Request',
        formid:'system'
      },
      // range:{
      //   realtor:{
      //     min:150,
      //     max:250,
      //     value:150,
      //     step:100
      //   },
      //   vip:{
      //     min:108,
      //     max:204,
      //     value:108,
      //     step:96
      //   }
      // },
      feedurl: '/1.5/form/forminput',
      adOnly:vars.adOnly,
      props:[],
      value:150,
      min:150,
      minHtml:'',
      max:250,
      maxHtml:'',
      step:100,
      msg:'',
      loading:false,
      _id:'',
      addr:'',
      city:'',
      days:15,
      rankSpot:0,
      showAll:false,
      inited:false,
      isAdmin:vars.isAdmin,
      isVipRealtor:vars.isVipRealtor,
      search:'',
      users:null,
      selectedRealtor:null,
      topTs:'',
      topuid:'',
      newTopTs:'',
      dispVar:    {
        defaultEmail:'',
        isCip:false,
        isRealtor:false,
        isApp:false,
        lang:'zh',
        isVipRealtor:false,
        isDevGroup:false,
        isAdmin: false,
        rltrTopAd:false,
        // isAllowedPredict:false,
        topUpRange:{},
        isRealGroup:false,
      },
      datas:[
        'defaultEmail',
        'isRealtor',
        'isVipPlus',
        'isApp',
        'isLoggedIn',
        'isVipRealtor',
        'hasFollowedVipRealtor',
        'topUpRange',
        'coreVer',
        'isRealGroup'
      ],
      sendEmailAddress:'',
      tokenKey: vars.prop.tokenTopListingKey,
      tokenM: vars.prop._id,
      showTokenBtn: false,
      showPaymentOpt: !vars.notShowPay
    };
  },
  beforeMount () {
    // if(this.isVipRealtor) {
    //   this.min = this.range.vip.min;
    //   this.max = this.range.vip.max;
    //   this.step = this.range.vip.step;
    //   this.value = this.range.vip.value;
    // }
    this.addr = vars.addr;
    this.city = vars.city;
    this.prov = vars.prov;
    this._id = vars.id;
    if(vars.user.eml && vars.user.eml.length){
      this.userForm.eml = Array.isArray(vars.user.eml)? vars.user.eml[0] : vars.user.eml;
    } else {
      this.userForm.eml = ''
    }
    this.adminMode();
  },
  beforeDestroy() {
    // 清理bus事件监听器
    window.bus.$off('token-exchange-success');
    // 清理标志
    window.exchangeNoRefresh = false;
  },
  mounted () {
    this.minHtml = '$'+this.min;
    this.maxHtml = '$'+this.max;
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus, self = this;
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.initRange(d)
      self.showTokenButton()
    });
    self.getPageData(self.datas,{},true);
    if (this.dispVar.defaultEmail){
      self.userForm.ueml = self.dispVar.defaultEmail;
    }
    // if (!vars.id) {
    //   return console.error('No id!');
    // }
    self.name = self._('RealMaster');
    var unt = '';
    if (vars.unt && unt.length && unt!='undefined') {
      unt = vars.unt+' ';
    } else {
      vars.unt = '';
    }
    if (vars.prop) {
      self.tlBcc = vars.prop.tlBcc;
      self.topTs = vars.prop.topTs;
      if (vars.prop.topuids) {
        const lastIdx = vars.prop.topuids.length - 1;
        self.topuid = vars.prop.topuids[lastIdx];
        // self.searchUser();
        window.bus.$emit('select-user-list', {uid:self.topuid});
      }
      self.calcTopTs();
    }
    self.desc = 'For: '+unt+vars.addr+' '+vars.city;
    if (vars.city && vars.prov) {
      // self.getTopUps({
      //   city:vars.city,
      //   prov:vars.prov
      // });
    }
    // setTimeout(function () {
    //   // self.loadJs();//will block vue render
    //   self.initHandler();
    // }, 100);
    window.addEventListener('popstate', function() {
      self.handler.close();
    });
    window.bus.$on('select-user',function(d) {
      self.selectRealtor(d.user);
    });
    // 监听token兑换成功事件
    window.bus.$on('token-exchange-success', function(data) {
      self.handleTokenExchangeSuccess(data);
    });
  },
  methods: {
    // 检查是否显示 Token 兑换按钮
    showTokenButton() {
      if (!this.dispVar.isRealGroup || !vars.prop || !vars.prop.rltr) {
        return;
      }
      let self = this;
      const rltrStr = String(vars.prop.rltr).toLowerCase();
      let result = /avion realty inc/i.test(rltrStr);
      if(result){
        window.exchangeNoRefresh = true;
        this.$set(this, 'showTokenBtn', true);
        setTimeout(() => {
          self.checkLoadTokenExchange()
        }, 100);
      }
    },
    // 处理token兑换成功的回调
    handleTokenExchangeSuccess(data) {
      var self = this;      
      let d = self.buildTopUpRequestData();
      d.days = 30;
      d.rltr = String(vars.prop.rltr).toLowerCase();
      d.sendReceipt = false;
      self.loading = true;
      self.callTopUpChargeApi(d);
    },
    checkLoadTokenExchange(){
      var self = this;
      if(this.showTokenBtn ){
        if(!this.showPaymentOpt){
          this.days = 30;
          this.calcTopTs();
        }
        let tokenExchangeBox = document.querySelector('#exchange-token') || this.$refs.exchange;
        if(tokenExchangeBox){
          tokenExchangeBox.click();
        }
      }
    },
    adminMode() {
      if (this.isAdmin) {
        let adminRange = {};
        
        // 扩展范围至60天
        for (let day = 1; day <= 60; day++) {
          let val = (day * this.step);
          adminRange[val] = day;
        }
        this.adminRange = adminRange;
      }
    },
    calcTopTs() {
      let topTs = new Date()
      if (this.topTs && new Date(this.topTs) >= new Date()) {
        topTs = new Date(this.topTs);
      }
      const tsNew = this.days*(24*60*60)*1000
      this.newTopTs = new Date(+topTs+tsNew)
    },
    onChangeVal(val){
      this.value = val;
    },
    // showStaging(){
    //   var url = 'http://www.realmaster.cn/mp/bba7b2bb1984ae8b3d365e92c6801c54a219b18060e8de354e460c0491529932cd1c6cdc880858aa82d3b04aa24a936a434b9e942d8cae9c07ac1ee5c4d8228df1f23c?lang=zh-cn&uid=RM_1031';
    //   RMSrv.showInBrowser(url);
    // },
    loadJs(){
      var self = this;
      var script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      document.body.appendChild(script);
      // self.initHandler();
      // onreadystatechange onload can not be trusted
    },
    initHandler(){
      var self = this;
      if (!window.Stripe) {
        return setTimeout(function () {
          self.initHandler()
        }, 100);
      }
      if (self.inited) {
        return;
      }

      // self.stripe = Stripe(window.stripePubKey);
      // self.inited = true;
    },
    showAllList(){
      var self = this;
      this.showAll = !this.showAll;
      // self.getTopUps({
      //   city:vars.city,
      //   prov:vars.prov
      // });
    },
    getRankFromVal(v){
      if (!this.props.length) {
        return 0;
      }
      var l = [];
      var curVal = new Date();
      for (let p of this.props) {
        if (!p.cur) {
          // l.push(p.topup_pts);
          l.push(new Date(p.topTs));
        } else {
          // curVal = p.topup_pts;
          curVal = new Date(p.topTs);
        }
      }
      // var realVal = v*100+curVal;
      var realVal = new Date(+curVal+v*1000*3600*24/5);
      l.push(realVal);
      l.sort((a,b)=>{
        return b-a;
      });
      for (var i = 0; i < l.length; i++) {
        if (l[i] == realVal) {
          return i;
        }
      }
      return l.length;
    },
    showPropDetail(p){
      var url = "/1.5/prop/detail/inapp?id="+p._id;//+'&mode='+self.appMapMode;
      url = this.appendDomain(url);
      RMSrv.closeAndRedirect(url);
      // var url = '/html/propDetailPage.html?id='+p._id;
      // // var plus = url.indexOf('?')>0?'&':'?';
      // var plus = '&';
      // plus += 'inframe=1';
      // url += plus;
      // url = this.appendDomain(url);
      // // console.log(url);
      // this.tbrowser(url);
    },
    // 构建TopUp请求参数
    buildTopUpRequestData() {
      var d = {
        id: vars.id,
        days: this.days,
        originalAmount: this.value * 100,
        chargeAmount: this.value * 100 * 113 / 100,
        addr: vars.addr,
        city: vars.city,
        unt: vars.unt,
        prov: vars.prov,
        cnty: vars.cnty,
        sendReceipt: this.sendReceipt,
        sendNotice: this.sendNotice,
        hideInfo: this.hideInfo,
      };
      if (this.tlBcc) {
        d.tlBcc = this.tlBcc;
      }
      const selectedRealtor = this.selectedRealtor;
      if (selectedRealtor && selectedRealtor._id) {
        d.topupuid = selectedRealtor._id;
        d.topuprole = selectedRealtor.isVip;
      }
      return d;
    },
    callTopUpChargeApi(requestData) {
      var self = this;
      return self.$http.post('/1.5/prop/topup/charge', requestData).then(
        function (ret) {
          self.loading = false;
          ret = ret.data;
          if (ret.ok) {
            self.msg = ret.msg;
            self.sendEmailAddress = ret.sendEmailAddress;
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          self.loading = false;
          ajaxError(ret);
        }
      );
    },
    custPurchase(e){
      if (this.isAdmin && !this.selectedRealtor && !this.hideInfo) {
        RMSrv.dialogConfirm('Realtor is not mandatory, but admin account will be used as topuid, please select hideInfo', null, '', ['Confirm', '']);
        return;
      }
      var self = this;
      
      // 构建请求参数
      var d = this.buildTopUpRequestData();
      if (self.loading) {
        return;
      }
      // 没有topup跳转sprite页面支付
      if (!vars.topup) {
        // return RMSrv.dialogAlert('Only available for admin')
        self.loading = true;
        // 安卓手机并且版本低于6.2.0提示更新
        if(RMSrv.isAndroid() && !(self.isNewerVer(self.dispVar.coreVer,'6.2.0'))){
          return this.confirmUpgrade(this.dispVar.lang,'Checkout only available in new version, please contact admin to topup or upgrade to new version for more features;');
        }
        // 使用get，打开新窗口，closepopup
        var  data = ''
        for (let k in d) {
          data += k+"="+d[k]+"&"
        }
        var url = '/1.5/prop/topup/createSession?'+data;
        RMSrv.getPageContent(url, '#callBackString', {toolbar:false}, function(val){
          if (val == ':cancel') {
            return;
          }
        });
        self.loading = false;
        return;
      }
      // 直接进入成功后处理页面
      var _doTopUp = function(r){
        if (r+'' !== '2') {
          self.loading = false;
          return;
        }
        self.callTopUpChargeApi(d);
      }
      if (self.isAdmin && self.selectedRealtor) {
        self.loading = true;
        var msg = self.sprintf(self._('You will top this listing %d days for %s'),self.days,self.selectedRealtor.nm || self.selectedRealtor.fnm)
        RMSrv.dialogConfirm(msg,
          _doTopUp,
          self._("Message"),
          [self._('Cancel'), self._('Confirm')]);
        return;
      }
      e.preventDefault();
    },
    updateBcc() {
      const self = this;
      const d = {
        _id:vars.id,
        tlBcc:self.tlBcc
      }
      self.$http.post('/1.5/prop/manage', d).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.tlBccMsg = true;
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    // showStrap(){
    //   var script;
    //   script = document.createElement('script');
    //   script.type = 'text/javascript';
    //   script.async = true;
    //   script.className = 'stripe-button';
    //   script.setAttribute('data-key', 'pk_test_xuuOG1SkPt8l46NikwVbp39m');
    //   script.setAttribute('data-amount', this.value*100);
    //   script.setAttribute('data-name', 'name');
    //   script.setAttribute('data-description', 'desc');
    //   script.setAttribute('data-locale', 'auto');
    //   script.setAttribute('data-currency', 'cad');
    //   script.onload = function(){
    //       console.log('loaded stripe');
    //   };
    //   script.src = 'http://checkout.stripe.com/checkout.js';
    //   document.getElementById('payForm').appendChild(script);
    // },
    processProps(l){
      for (let p of l) {
        if (p._id == vars.id) {
          p.cur = true;
          break;
        }
      }
      return l;
    },
    getTopUps (cfg) {
      var self = this;
      self.loading = true;
      var d = {
        city:cfg.city,
        prov:cfg.prov
      };
      self.$http.post('/1.5/prop/topup/list', d).then(
        function (ret) {
          ret = ret.data;
          self.loading = false;
          if (ret.ok) {
            self.props = self.processProps(ret.props);
            self.rankSpot = self.getRankFromVal(self.value)+1;
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },

    selectRealtor(u) {
      this.selectedRealtor = u
      // let type;
      // if (this.selectedRealtor.isVip) {
      //   type = 'vip';
      // } else {
      //   type = 'realtor'
      // }
      this.initRange(u)
    },
    initRange(range){
      if(range && range.topUpRange){
        this.min = range.topUpRange.min;
        this.max = range.topUpRange.max;
        this.step = range.topUpRange.step;
        this.value = range.topUpRange.value;
        this.adminMode()
        this.minHtml = '$'+this.min;
        this.maxHtml = '$'+this.max;
      }
    },
    toggleSendReceipt() {
      this.sendReceipt = !this.sendReceipt;
    },
    toggleShowInfo() {
      this.hideInfo = !this.hideInfo;
    },
    toggleSendNotice() {
      this.sendNotice = !this.sendNotice;
      if (this.sendNotice) {
        this.sendReceipt = false;
      } else {
        this.sendReceipt = true;
      }
    }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.btn-wrapper{
  margin: 0 15px;
}
.btn-wrapper .btn-block{
  font-size: 16px;
  height: 41px;
  padding: 11px 0;
}
.content{
  background: #f1f1f1;
}
.card{
  margin: 15px 0 0 0;
  background: white;
  border: none;
  border-radius: 0;
}
.card.top-listings{
  margin-top: 1px;
}
.card.desc{
  background: #f1f1f1;
  padding: 5px;
  text-align: center;
}
.card.desc img{
  width: 70%;
}
.card.desc .desc{
  padding: 10px;
  text-align: left;
}
.card .tl{
  font-size: 18px;
  color: #e03131;
  text-align: center;
}
.card .desc{
  font-size: 14px;
  color: #666;
}
.card.payment .addr{
  font-size: 15px;
  /*color: #428bca;*/
  font-weight: bold;
  padding: 10px 15px 2px;
  text-align: left;
}
.card .rank-in-pay{
  padding: 1px 0 15px 15px;
}
.rank-in-pay .pull-right{
  padding-right: 20px;
  font-size: 15px;
  color: #428bca;
}
.rank-in-pay .pull-right .fa{
  padding-left: 6px;
}
.card .city{
  text-align: center;
  padding: 0 0 10px 0;
}
.card.how-it-works{
  text-align: center;
}
.card.how-it-works img{
  width: 50px;
}
.card.how-it-works .desc{
  text-align: left;
}
.content.success{
  text-align: center;
  background: white;
  padding: 80px 20px 0 20px;
}
.content.success .msg{
  font-size: 21px;
  color: black;
  padding-bottom: 15px;
  font-weight: 400;
}
.content.success .desc{
  font-size: 13px;
  color: #666;
  text-align: left;
}
.content.success img{
  width: 100px;
}
/*.msg{
  border: 10px;
  padding: 20px;
  text-align: center;
  font-size: 17px;
  background:#D4FAAA;
  border: 1px solid #5cb85c;
}*/
.fa-check-circle{
  color: #80D820;
  padding-right: 7px;
}
#payForm{
  text-align: center;
  padding: 10px;
}
.prop-list > div{
  display: inline-block;
  vertical-align: top;
  padding: 10px;
  /*border-bottom: 1px solid #f1f1f1;*/
}
.prop-list .rank{
  color: #666;
  font-size: 14px;
  padding-left: 15px;
}
.prop-list .rank{
  width: 40px;
}
.prop-list .value{
  text-align: right;
  width: 150px;
  color: #666;
  font-size: 13px;
  padding-right: 15px;
}
.prop-list .addr{
  width: calc(100% - 190px);
  color: #428bca;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.prop-list .addr.cur{
  font-weight: bold;
}
.tl{
  font-size: 17px;
  color: #666;
  padding: 20px 10px 10px 10px;
}
.desc{
  font-size: 14px;
  color: #666;
  padding: 10px 15px;
}
.how-to-start .desc {
  padding:10px 15px 20px;
}
/*.value{
  width: 40px;
  display: inline-block;
  vertical-align: top;
}
.range{
  display: inline-block;
  width: calc(100% - 40px);
  padding-right: 60px;
  padding-left: 25px;
  vertical-align: top;
}*/
.range-wrapper{
  height: 30px;
  margin-right: 25px;
  margin-top: 45px;
}
.topup-date {
  font-size: 15px;
  text-align: left;
  padding: 0 15px;
}
/* .staging{
  margin: 15px 0 0 0;
  line-height: 0;
}
.staging img{
  width: 100%;
  height: auto;
  padding: 10px;
  background: white;
} */
/* .admin-panel {
  margin: 10px 15px;
  padding: 10px;
  background-color:lightgray;
}
.admin-input {
  width: calc(100% - 30px);
}
.user {
  font-size: 14px;
  margin-top: 10px;
  padding: 5px;
}
.user.selected {
  background-color:#5cb85c;
  color:#fff;
}
.user-list {
  overflow-y: scroll;
  max-height: 300px;
}
.user-list-no-result {
  text-align:center;
  margin-top:10px;
} */
.sendEmail {
  margin: 10px 15px;
  display:inline-block;
  vertical-align:middle;
}
.sendEmail::before {
  margin-right: 5px;
  display: inline-block;
  vertical-align: middle;
  font-size:24px;
}
.sendEmail.fa-check-square-o::before {
  color:#5cb85c;
}
#exchange-token {
  height: 110px;
  width: 100%;
  border: 0;
  padding: 7px 15px;
  background: #fff;
  margin: 15px 0;
}
</style>
