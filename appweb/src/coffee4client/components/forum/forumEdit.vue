<template lang="pug">
doctype html
#forum-main(v-cloak)
  //- track-log(:disp-var="dispVar")
  header.bar.bar-nav(v-if="dispVar.isApp && !inFrame")
    span.icon.fa.fa-back.pull-left(@click="back()")
    //- span.pull-right.bar-right(@click="publish()")
    //-   |{{_('Publish')}}
    h1.title {{_("Edit Post")}}
  confirm-with-message(:on-confirm='deletePost')
  div#FormSelectModal.modal
    app-form-select-modal
  city-select-modal(:need-loc='true', :cur-city="curCity", show-prov-bar=true)
  //- img-select-modal
  div.mask(v-if="showMask", @click="clearModals()")
  img-preview-modal
  flash-message
  create-nickname(:on-confirm="saveNickName", :on-cancel="cancelNickName", :is-admin="dispVar.forumAdmin")
  div.delete-tag-confirm.confirm-modal(v-if="showTagDialog.del")
    div {{_("Tag")}} {{currentTag}} {{_("will be removed from all the post.")}}
    div(style="margin-top:20px")
      button.btn.btn-half(@click="confirmTagManageDialog('del')") {{_('Confirm')}}
      button.btn.btn-half(@click="cancelTagManageDialog('del')") {{_('Cancel')}}
  div.edit-tag-confirm.confirm-modal(v-if="showTagDialog.edit")
    div {{_("Edit Tag:")}}
    div
      input(type="checkbox",v-model='tagAdminOnly',id="tagAdminOnly")
      label(for="tagAdminOnly") {{_('Admin')}}
      label(style="padding-left:10px;") {{_('sort')}}
      input.tag-sort-input(type="text", v-model='tagSortKey',@blur='tagSortKey = checkInput(tagSortKey)')
    div
      input.newtag(type="text",v-model='newTag',@blur='newTag = checkInput(newTag)')
      div(style="width: 50%;float: right;padding: 10px;")
        button.btn.btn-half(style="padding:0; height:44px", @click="confirmTagManageDialog('edit')") {{_('Confirm')}}
        button.btn.btn-half(style="padding:0; height:44px", @click="cancelTagManageDialog('edit')") {{_('Cancel')}}

  div.select-topic.confirm-modal(v-if="selectTopic")
    div(v-for ="tp in topicList", style="padding:10px")
      div.field-name {{tp}}
      div.pull-right(@click="checkTopic(tp)")
        i.fa.fa-check-square-o(v-show="checkedTopicList[tp]")
        i.fa.fa-square-o(v-show="!checkedTopicList[tp]")
    button.btn.btn-long.btn-primary(style="padding:0; height:44px", @click="closeTopicSelect()") {{_('Confirm')}}

  div#forumEditContainer(v-bind:class="{web:!dispVar.isApp || inFrame}")
    div.field-header(v-if="showGroup", style="margin-bottom:5px;")
     label {{_('Group','forum')}}*
     select(v-model="post.gid", style="width:200px;float:right", :disabled="isedit")
       option(value="") {{_('None','forum')}}
       option(v-for="grp in dispVar.userGroups",v-bind:value="grp._id") {{grp.nm}}
    div.table-view-cell.row
      div.field-name
        label {{_('Title')}}*
      div.field-input
        input(type="text",required,v-model='post.tl',@blur='post.tl = checkInput(post.tl)')
    div.table-view-cell.row
      div.field-name(style="width:100px")
        label {{_('Select City')}}*
      div.pull-right.city(@click="getCityList()")
        div.cityName(v-if="curCity.o || curCity.p || curCity.cnty") {{_(curCity.o || curCity.p || curCity.cnty,'city') || _('City','forum')}}
        div.cityName(v-if="curCity.ncity") {{_('No City','forum')}}
        span.icon.fa.pull-right.fa-angle-down(style="padding-left:5px")
    div.field-header {{_('Content')}}*
    div.table-view-cell.row.post-content
      textarea(rows="5", v-model="post.m", :placeholder="_('No self promoting, political related, or personal attacking articles allowed. Will be removed promptly.')",@blur='post.m = checkInput(post.m)')
    div(style="color: grey;font-size: 14px;padding-left: 10px;")
      | {{_("[pic#] to insert pictures in place.")}}

    div.field-header {{_('Photo')}}
    div
      div.forum-photos
        div.image(v-for="photo in post.photos", track-by="$index",:style="{ 'background-image': getThumbUrl(photo)}" , @click="previewPic(photo,'photo')")
        span.image.new-img(@click="showImgSelectModal('photo');")
          div.icon.icon-plus

    div.video(v-if="dispVar.isPropAdmin")
      div.field-header {{_('Video')}}
      div.row
        label {{_('Type','video')}}
        span.pull-right.type
          input(type="checkbox",v-model='post.vidRecord')
          span {{_('Record')}}
          input(type="checkbox",v-model='post.vidLive')
          span {{_('Live')}}
      div.row
        label {{_('Schedule','video')}}
        //- span.pull-right {{post.ohv}}
        span.input-wrapper.pull-right
          input(type="text",required,v-model='post.ohv',placeholder="2020-04-19 14:00-16:00",@blur='post.ohv = checkInput(post.ohv)')
      div.row
        label {{_('Stream URL','video')}}
        span.input-wrapper.pull-right
          input(type="text",required,v-model='post.vidUrl',@blur='post.vidUrl = checkInput(post.vidUrl)')
      div.row
        label {{_('Aspet ratio','video')}}
        span.input-wrapper.pull-right
          input(type="text",required,v-model='post.ratio',placeholder="16:9",@blur='post.ratio = checkInput(post.ratio)')
      div.row.agent
        label {{_('Agent','video')}}
        //- listing agent panel
        agent-setting(:sponsorAgents='sponsorAgents', :sponsorGroups='sponsorGroups', type="App", :hideRequestInfo="true", :hideGroup="true")
      div.row
        label {{_('Listing ID','video')}}(_id)
        //- span.pull-right {{post.sid}}
        span.input-wrapper.pull-right
          input(type="text",required,v-model='post.pid',@blur='post.pid = checkInput(post.pid)')

    div.category-wrapper(v-if="dispVar.isMerchant || dispVar.forumAdmin")
      div.row(v-if="dispVar.isPropAdmin")
        label {{_('Post Src')}}:
        span.input-wrapper.pull-right
          //- input(type="text",v-model="post.src")
          select(v-model="post.src")
            option(v-for="i in srcs",v-bind:value="i") {{i}}
      div.category
        label {{_('Category','yellowpage')}}*
        select(v-model="category")
          option(v-for="category in categories",v-bind:value="category") {{category.t}}

      div.sub-wrapper(style="font-size:15px;")
        label(style="padding-right: 10px;") {{_('Sub','yellowpage')}}*
        div.subcategory(v-for="sub in category.sub", @click="clickSubCate(sub)", :class="{active: isSelected(sub)}")
          span {{sub.val}}


    div.table-view-cell.row(v-if="dispVar.forumAdmin")
      label {{_('Show In Renovation','forum')}}
      select(style="width: 120px;float: right;",v-model="post.reno")
        option {{_('Please Choose Option','forum')}}
        option(value='') {{_('None','forum')}}
        option(value=1) {{_('Renovation','forum')}}
        option(value=2) {{_('Sticky Renovation','forum')}}
    div.field-header(v-show="!post.gid") {{_('Tags')}}
      span.pull-right.icon.icon-plus(v-if="dispVar.forumAdmin",@click="showTagMangeDialog('add')")
    div.table-view-cell(v-if="showTagDialog.add")
      div
        input(type="checkbox",v-model='tagAdminOnly',id="tagAdminOnlyAdd")
        label(for="tagAdminOnlyAdd",style="padding-left:5px") {{_('Admin Only')}}
        label(style="padding-left:10px") {{_('sort')}}
        input.tag-sort-input(type="text",v-model='tagSortKey',@blur='tagSortKey = checkInput(tagSortKey)')
      div.field-name.tag-input
        input(type="text",v-model='newTag',@blur='newTag = checkInput(newTag)')
      div(style="width:50%;float:right;padding-left: 10px;")
        button.btn-primary.btn-half(@click="confirmTagManageDialog('add')")
          | {{_('Confirm')}}
        button.btn-primary.btn-half(@click="cancelTagManageDialog('add')")
          | {{_('Cancel')}}
    div.table-view-cell.tag(v-show="!post.gid", v-for ="tag in tagList", style="padding-right:10px")
      div.field-name {{tag.key}}
      div.pull-right(@click="check(tag.key)")
        i.fa.fa-check-square-o(v-show="checkedTagList[tag.key]")
        i.fa.fa-square-o(v-show="!checkedTagList[tag.key]")
      span.pull-right.tag-delete(v-if="dispVar.forumAdmin")
        span.icon.icon-trash(@click="showTagMangeDialog('del', tag)")
        span.icon.icon-edit(@click="showTagMangeDialog('edit', tag)")

    div.padding-bottom-20(v-if="!post.gid && (dispVar.forumAdmin || dispVar.canWebComment)")
      div.field-header {{_('Topics')}}
      div.table-view-cell.row
        div.field-name.pull-left.tag-input(v-if="dispVar.forumAdmin")
          input(type="text",v-model='post.tp', @blur="onTopicChange()")
        div.pull-left(style="width:50%;padding-left: 10px;")
          button.btn-primary(style="height: 50px;",v-if="topicList.length",@click="showTopicSelect()")
            | {{_('Belonged topic')}}
    div.padding-bottom-10(v-if="dispVar.forumAdmin")
      div.field-header {{_('AD in list')}}
        div.pull-left(style="width:30px;")
          input(type="checkbox",v-model='adInlist')
      div.field-header {{_('Top Ad')}}
      div.field-header
        //- input(type="checkbox",v-model='adIntop')
        //- span {{_('AD in top')}}
        //- input(type="checkbox",v-model='adInBtop')
        //- span {{_('AD in top bottom')}}
        //- 通过分数排名，高分在前低分在后，默认没有分数
        div(style="padding-bottom:10px;") {{_('Rank')}}
          span(style='font-size: 14px;font-weight: normal;') {{_('High scores are shown at the top')}}
        label(v-for='i in 5')
          input.radio-rank(type="radio",name='rank',:value='i',v-model='rank')
          //-  @click='addRank(i)'
          | {{i}}
        span.pull-right(@click='unsetRank()')
          | {{_('Unset')}}
      div.table-view-cell(style="padding:10px;")
        div.field-name.pull-left.tag-input(style="width:100%")
          input(type="text",:placeholder="_('Please enter URL for Top Ads')", v-model='post.adTop',@blur='post.adTop = checkInput(post.adTop)')
        div
          div.forum-photos(style="padding-left:0px;")
            div.image(v-if="adTopPhoto", :style="{ 'background-image': getThumbUrl(adTopPhoto)}" , @click="previewPic(adTopPhoto,'adTopPhoto')")
            span.image.new-img(v-if="!adTopPhoto",@click="showImgSelectModal('adTopPhoto');")
              div.icon.icon-plus
      div(v-show="!adInlist")
        div.field-header {{_('Bottom Ad')}}
        div.table-view-cell(style="padding:10px;")
          div.field-name.pull-left.tag-input(style="width:100%")
            input(type="text",:placeholder="_('Please enter URL for End Ads')", v-model='post.adBottom',@blur='post.adBottom = checkInput(post.adBottom)')
          div
            div.forum-photos(style="padding-left:0px;")
              div.image(v-if="adBottomPhoto", :style="{ 'background-image': getThumbUrl(adBottomPhoto)}" , @click="previewPic(adBottomPhoto,'adBottomPhoto')")
              span.image.new-img(v-if="!adBottomPhoto",@click="showImgSelectModal('adBottomPhoto');")
                div.icon.icon-plus

    div(v-if="dispVar.forumAdmin || dispVar.formOrganizer")
      div.field-header(v-show="dispVar.forumAdmin && !post.formid") {{_('Show Request Info')}}
        div.pull-left(style="width:30px;")
          input(type="checkbox",v-model='post.showRequestInfo')
      div.table-view-cell.row(v-show="!post.formid")
        div.field-name.pull-left.tag-input(style="width:100%")
          input(type="text",:placeholder="_('Please enter form Title')", v-model='post.requestInfotl',@blur='post.requestInfotl = checkInput(post.requestInfotl)')
      div.field-header(v-show="!post.requestInfotl")
        span.btn(@click="getFormList()") {{_('Insert Form')}}
        span(v-if="formName") {{formName}}
        span.btn.pull-right(v-if="formName", @click="deleteForm()") {{_('Delete Form','form')}}

    div(v-if="dispVar.forumAdmin")
      div.field-header {{_('Meta Keywords')}}
      div.table-view-cell.row.post-content
        textarea(rows="2", v-model="post.meta_keywords",@blur='post.meta_keywords = checkInput(post.meta_keywords)')
      div.field-header {{_('Meta Description')}}
      div.table-view-cell.row.post-content
        textarea(rows="3", v-model="post.meta_desc",@blur='post.meta_desc = checkInput(post.meta_desc)')

  div.bar.bar-standard.bar-footer
    span.icon.icon-trash.pull-left(v-if="post._id && computedAdmin && !post.del", @click="deletePostConfirm('delete')")
    span.icon.icon-trash.pull-left(v-if="computedAdmin && post.del", style="color:#E03131", @click="deletePostConfirm('db_delete')")
    span.fa.fa-undo.post-recover.icon.pull-left(v-if="computedAdmin && post.del",  @click="deletePostConfirm('recover')")

    span.publish-btn.pull-right(@click="publish()", :disabled="publishing")
      |{{_('Publish')}}
    div.pull-right(style="padding-right:10px;", v-if="!post.del")
      span.btn.btn-negative.pull-right(v-if="post_sticky && post.sticky=='global' && computedAdmin", @click="sticky(false)")
        |{{_('unSticky')}}
      span.btn.btn-negative.pull-right(v-if="post_sticky && post.sticky=='city' && computedAdmin", @click="sticky(false)")
        |{{_('unSticky')}}
      span.btn.btn-negative.pull-right(v-if="post._id && post.status!=='new' && !post_sticky && computedAdmin", @click="sticky('global')")
        |{{_('Sticky')}}

  div(style='top: 50%; position: absolute;left: 50%;')
    div.pull-spinner(v-show='publishing',style='display:block')
</template>

<script>
import CitySelectModal from '../frac/CitySelectModal.vue'
import FlashMessage from '../frac/FlashMessage.vue'
import rmsrv_mixins from '../rmsrv_mixins'
import pagedata_mixins from '../pagedata_mixins'
import ConfirmWithMessage from './ConfirmWithMessage.vue'
// import imgSelectModal from '../frac/ImgSelectModal.vue'
import imgPreviewModal from '../frac/ImgPreviewModal.vue'
import file_mixins from '../file_mixins'
import forum_mixins from './forum_mixins'
import forum_common_mixins from './forum_common_mixins'
import yellowpage_mixins from '../yellowpage/yellowpage_mixins'
// import TrackLog from '../frac/TrackLog'
import CreateNickname from './CreateNickname.vue'
import AppFormSelectModal from '../form/appFormSelectModal.vue'
import AgentSetting from '../project/agentSetting.vue'

export default {
  mixins: [pagedata_mixins,file_mixins,forum_mixins,forum_common_mixins,yellowpage_mixins],
  data () {
    return {
      datas:[
        'isCip',
        'isApp',
        'userCity',
        'isLoggedIn',
        'lang',
        'isPropAdmin',
        'forumAdmin',
        'sessionUser',
        'canWebComment',
        'reqHost',
        'isMerchant',
        'isVipUser',
        'formOrganizer',
        'userGroups',
        'shareHostNameCn'
      ],
      dispVar: {
        userCity:    {o:'Toronto',n:'多伦多'},
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       false,
        isCip:       false,
        forumAdmin:  false,
        sessionUser: false,
        canWebComment: false,
        reqHost:'www.realmaster.com',
        isMerchant:false,
        isVipUser: false,
        formOrganizer: false,
        userGroups:[]
      },
      tagList: [],
      checkedTagList: {},
      post: {
        tags:[],
        photos:[],
        spuids:[],
        spgids:[],
        del:false,
        gid:vars.gid,
        vidLive:false,
        vidRecord:false,
        src:'post',
      },
      oldPost:{},
      newTag:'',
      tagSortKey:0,
      tagAdminOnly:false,
      showTagDialog:{add: false, edit: false, del: false},
      currentTag:'',
      curCity:{o:'Toronto',n:'多伦多',p:'Ontario',pn:'安大略省'},
      currentPic:'',
      showMask: false,
      publishing: false,
      post_sticky: false,
      selectTopic: false,
      checkedTopicList: [],
      topicList:[],
      selectPhotoType: 'photo',
      adTopPhoto:'',
      adBottomPhoto:'',
      adInlist:false,
      update_mt:false,
      // adIntop: false,
      // adInBtop: false,
      // vidLive:false,
      // vidRecord:false,
      rank:null,
      category:{sub:[],id:'none',t:'None'},
      srcs:['news','wecard','property','psch','sch','video','post','building'],
      subcate:[],
      from:vars.from,
      formName:'',
      isedit: false,
      inFrame:vars.inFrame,
      sponsorAgents:[],
      sponsorGroups:[],
    };
  },
  methods: {
    back() {
      document.location.href = vars.d || window.history.back();
    },
    clickSubCate(sub) {
      var index = this.subcate.indexOf(sub.key);
      if (index >=0 ){
        this.subcate.splice(index,1);
      } else {
        this.subcate.push(sub.key);
      }
    },
    isSelected(sub) {
      var index = this.subcate.indexOf(sub.key);
      return index>=0
    },
    clearModals() {
      this.showMask = false;
      this.showTagDialog.add = false;
      this.showTagDialog.edit = false;
      this.showTagDialog.del = false;
      this.selectTopic = false;
      window.bus.$emit('close-edit-nickname');
    },
    onTopicChange() {
      var self = this;
      self.post.tp = self.checkInput(self.post.tp);
      if (self.oldPost.tp && self.post.tp.trim() && self.oldPost.tp != self.post.tp) {
        return window.bus.$emit('flash-message',self.$parent._('change Topic name will change all the topic belongs to it'));
      }
      if (self.oldPost.tp && !self.post.tp.trim()) {
        return window.bus.$emit('flash-message',self.$parent._('delete Topic will remove it from all related post'));
      }
      this.showMask = false;
    },
    checkTopic(tp) {
      var tmp = {}, val;
      val = this.checkedTopicList[tp]?false:true;
      tmp[tp] = val;
      this.checkedTopicList = Object.assign({}, this.checkedTopicList, tmp);
    },
    showTopicSelect() {
      var self = this;
      this.selectTopic = true;
      this.showMask = true;
      if (!self.post.tpbl) return;
      self.post.tpbl.forEach(function(tp) {
        var intagList = self.topicList.find(function(_tp) {
          return tp === _tp;
        })
        if (intagList)
          self.checkedTopicList[tp] = true;
      });
    },
    closeTopicSelect() {
      this.selectTopic = false;
      this.showMask = false;
    },
    toggleTitleDialog() {
      toggleModal('titleDialog');
    },
    toggleContentDialog() {
      toggleModal('contentDialog');
    },
    goTo(url) {
      if (vars.d)
        url = vars.d;
      window.location.href=url;
    },
    check(tag){
      var tmp = {}, val;
      val = this.checkedTagList[tag]?false:true;
      tmp[tag] = val;
      if(tmp.HOT) {
        this.update_mt = true;
      }
      this.checkedTagList = Object.assign({}, this.checkedTagList, tmp);
    },
    setLang(lang) {
      this.post.lang = lang;
      this.getTagList(lang);
    },
    initPost(id, gid) {
      var self = this;
      self.$http.post('/1.5/forum/detail/' + id,{isedit: true, gid: gid}).then(
        function(ret) {
          if (ret.body.ok) {
            ret.body.post.del = ret.body.post.del || false;
            self.post = Object.assign(self.post, ret.body.post);
            self.adInlist = self.post.adInlist || false;
            // self.adIntop = self.post.adIntop || false;
            // self.adInBtop = self.post.adInBtop || false;
            self.rank = self.post.rank || null;
            self.formName = self.post.formName || '';
            if(self.post.tp) {
              self.topicList.splice(self.topicList.indexOf(self.post.tp), 1);
            }
            self.post_sticky = self.post.sticky || false;
            self.adTopPhoto = self.post.adTopPhoto || '';
            self.adBottomPhoto = self.post.adBottomPhoto || '';
            self.oldPost=(JSON.parse(JSON.stringify(ret.body.post)));
            // self.oldPost =  Object.assign({}, ret.body.post);
            if (!self.post) return;
            self.curCity.o = self.post.city;
            self.curCity.p = self.post.prov;
            self.curCity.cnty = self.post.cnty;
            self.curCity.ncity = self.post.ncity;
            self.curCity.n = self.$parent._(self.post.city);
            if (ret.body.post.agents) {
              self.sponsorAgents = ret.body.post.agents;
            }
            if (ret.body.sponsorGroups) {
              self.sponsorGroups = ret.body.sponsorGroups;
            }
            self.getTagList(function(){
              if (!self.post.tags) return;
              self.post.tags.forEach(function(tag) {
                var intagList = self.tagList.find(function(obj) {
                  return tag === obj.key;
                })
                if (intagList)
                  self.checkedTagList[tag] = true;
              });
            });
            if (self.post.category) {
              self.category = this.categories.find(function(c) {
                return c.id == self.post.category;
              });
              self.subcate = self.post.subcate;
            }
          } else if(ret.e){
            return window.bus.$emit('flash-message',ret.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      )
    },
    getCityList() {
      window.bus.$emit('select-city', {});
    },
    getFormList() {
      window.bus.$emit('show-form-list', {});
      toggleModal('FormSelectModal');
    },
    deleteForm() {
      this.post.formid = '';
      this.formName = this.post.formName = '';
    },
    showImgSelectModal(type) {
      var self = this;
      self.selectPhotoType = type;
      // toggleModal('imgSelectModal');
      // window.bus.$emit('show-img-modal', {});
      var opt = {
      url :'/1.5/img/insert'
      }
      insertImage(opt,(val)=>{
        // console.log(val)
          //   val = '{"picUrls":["http://file.test:8091/img/G/BWMB/BHA.png","http://file.test:8091/img/G/BWMB/BHB.png"]}
        if (val == ':cancel') {
            console.log('canceled');
            return;
        }
        try {
          // alert(ret)
          var ret = JSON.parse(val);
          var picUrls = ret.picUrls;
          if(self.selectPhotoType == 'adTopPhoto'){
            self.adTopPhoto = self.post.adTopPhoto = picUrls[0];
          }
          else if (self.selectPhotoType == 'adBottomPhoto') {
            self.adBottomPhoto =self.post.adBottomPhoto = picUrls[0];
          }
          else {
            for(var pic of picUrls) {
              if (self.post.photos.indexOf(pic) === -1) {
                self.post.photos.push(pic);
              }
            }
          }
        } catch (e) {
          console.error(e);
        }
      })
    },
    formatDiff(diffArray) {
      var result = [];
      for (var key in diffArray) {
        var obj = {};
        var diff = diffArray[key];
        if(diff.added || diff.removed) {
          obj.a = diff.added;
          obj.c = diff.count;
          obj.v = diff.value;
          obj.r = diff.removed;
        } else {
          obj.c = diff.count
        }
        result.push(obj);
      }
      return result;
    },
    publish() {
      //set forum nickname
      var self = this;
      if(self.dispVar.sessionUser.fornm) {
        self.publishPost();
      } else {
        self.showMask = true;
        return window.bus.$emit('show-edit-nickname');
      }
    },
    saveNickName(nickname) {
      var self = this;
      self.dispVar.sessionUser.fornm = nickname;
      self.showMask = false;
      self.publishPost();
    },
    cancelNickName() {
      this.showMask = false;
    },
    findAgentIndex(array, u) {
      return array.findIndex(function(agent){
        return agent._id.toString() == u._id.toString();
      })
    },
    computeUids(fromArray, uidArray, emlArray) {
      // to compute uids for select group
      let array = [];
      for (let u of fromArray) {
        if (typeof u.eml == 'Array') {
          u.eml = u.eml[0]
        }
        if (emlArray || uidArray) {
          if (emlArray) {
            emlArray.push(u.eml);
          }
          if (uidArray) {
            uidArray.push(u._id);
          }
        } else {
          array.push(u._id);
        }
      }
      return array;
    },
    publishPost() {
      if (this.publishing==true) {
        return;
      }
      var self = this;
      self.post.adInlist = self.adInlist;
      // self.post.adIntop = self.adIntop;
      // self.post.adInBtop = self.adInBtop;
      self.post.rank = self.rank;
      self.post.category = this.category.id;
      self.post.subcate = self.subcate;
      if (self.post.gid) {
        var grp = self.dispVar.userGroups.find(function(_grp){
          return _grp._id == self.post.gid;
        })
        self.post.gnm = grp.nm;
      }
      if (!self.post.tl) {
        return window.bus.$emit('flash-message', self.$parent._("Title needed"));
      }
      if (!self.post.m) {
        return window.bus.$emit('flash-message', self.$parent._("Content needed"));
      }
      if (self.post.m.indexOf('data:image')>=0) {
        return window.bus.$emit('flash-message', self.$parent._("Please remove embeded pictures or replace it",'forum'));
      }
      if (self.post.vidLive && self.post.vidRecord) {
        return window.bus.$emit('flash-message','only one video type is supported');
      }
      self.post.tags = [];
      Object.keys(self.checkedTagList).forEach(function(key) {
        if(self.checkedTagList[key] == true) {
          self.post.tags.push(key);
        }
      });
      if (Object.keys(self.checkedTopicList).length > 0) {
        self.post.tpbl = [];
        Object.keys(self.checkedTopicList).forEach(function(key) {
          if(self.checkedTopicList[key] == true) {
            self.post.tpbl.push(key);
          }
        });
      }
      self.post.city = self.curCity.o;
      self.post.prov = self.curCity.p;
      self.post.cnty = self.curCity.cnty;
      self.post.ncity = self.curCity.ncity;
      self.post.src = self.post.src || 'post';
      if (!self.post.city &&  !self.post.prov && !self.post.cnty && !self.post.ncity) {
        return window.bus.$emit('flash-message', self.$parent._('City Needed'));
      }
      var param = {post:self.post};
      param.oldtp = self.oldPost.tp;
      // if (Object.keys(self.oldPost).length) {
      //   param.diff_tl = this.formatDiff(JsDiff.diffChars(self.oldPost.tl, self.post.tl));
      //   param.diff_m = this.formatDiff(JsDiff.diffChars(self.oldPost.m, self.post.m));
      //   if (self.post.tp!=null) {
      //     if (self.oldPost.tp==null) self.oldPost.tp = ''
      //     param.diff_tp = this.formatDiff(JsDiff.diffChars(self.oldPost.tp, self.post.tp));
      //   }
      //   if (!self.oldPost.tags && self.post.tags)
      //     param.diff_tag = self.post.tags;
      //   if (self.oldPost.tags && self.post.tags)
      //     param.diff_tag = this.formatDiff(JsDiff.diffArrays(self.oldPost.tags, self.post.tags));
      //   if (self.oldPost.city !== self.post.city)
      //     param.diff_city = self.post.city
      //   if (self.oldPost.prov !== self.post.prov)
      //     param.diff_prov = self.post.prov
      //   if (self.oldPost.photos && self.post.photos)
      //     param.diff_photo = this.formatDiff(JsDiff.diffArrays(self.oldPost.photos, self.post.photos));
      // }
      if (self.oldPost.tp && self.post.tp.trim() && self.oldPost.tp!==self.post.tp) {
        param.tp_action = 'edit';
      }
      if (self.oldPost.tp && !self.post.tp.trim()) {
        param.tp_action = 'del';
      }

      if(self.update_mt || self.oldPost.tl !== self.post.tl || self.oldPost.tp !== self.post.tp || self.oldPost.m !== self.post.m || JSON.stringify(self.oldPost.tpbl) !== JSON.stringify(self.post.tpbl))
        param.update_mt = true;

      if (JSON.stringify(self.post).length > 500000) {
        return window.bus.$emit('flash-message', self.$parent._('Content too large'));
      }
      if (this.post.gid)
        param.gid = this.post.gid;
      if (this.post.src == 'building'){
        param.post._id = vars.id;
      }
      self.publishing = true;
      self.post.spuids = []
      self.post.spgids = []
      self.computeUids(self.sponsorAgents,self.post.spuids);
      self.computeUids(self.sponsorGroups,self.post.spgids);
      if (self.post.ohv && !/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}-\d{2}:\d{2}$/.test(self.post.ohv)) {
        return window.bus.$emit('flash-message', 'Schedule format! expect: 2020-04-19 14:00-16:00');
        return;
      }
      function handleRet(){
        if (self.dispVar.isApp) {
          window.history.back();
        } else {
          window.top.location.href = '/forum/list?1=1';
        }
      }
      self.$http.post('/1.5/forum/edit',param).then(
        function (ret) {
          self.publishing = false;
          ret = ret.data;
          if (ret.ok) {
            self.err = '';
            // console.log('++++',ret)
            handleRet()
            // var url;
            // if (vars.d) {
            //     url= decodeURIComponent(vars.d);
            // } else if (self.dispVar.isApp){
            //   url = '/1.5/forum';
            // } else {
            //   url = '/forum/list?1=1';
            // }
            // window.top.location.href = url;
          } else {
            if (ret.forBlk) {
              console.log('blocked');
              handleRet()
            } else {
              return window.bus.$emit('flash-message', ret.e);
            }
          }
        },
        function (ret) {
          self.publishing = false;
          ajaxError(ret);
        }
      );
    },

    tagDoPost(params, cb){
      var self = this;
      self.$http.post('/1.5/forum/tag', params).then(
        function (ret) {
          if (ret.data.ok) cb(ret.data)
          else return window.bus.$emit('flash-message', ret.data.e);
        },
        function (ret) {
          ajaxError(ret);
        });
    },
    addTagConfirm() {
      var self = this;
      if (!self.newTag || !self.newTag.trim())
        return window.bus.$emit('flash-message', self.$parent._('no new tag value'));
      var params = {tag:self.newTag, action:'add',tagSortKey:self.tagSortKey, adminOnly:self.tagAdminOnly};
      // params.lang = self.post.lang;
      self.tagDoPost(params, function(ret) {
        self.tagList.push(ret.newTag);
        self.newTag = '';
        self.tagSortKey = 0;
        self.showTagDialog.add = false;
      });
    },
    getTagList(cb) {
      var self = this;
      self.$http.get('/1.5/forum/tags').then(
        function (ret) {
          if (ret.data.ok) {
            self.tagList = ret.data.tags || [];
            if (cb)
              cb()
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        });
    },
    deleteTag() {
      var self = this;
      self.tagDoPost({tag:self.currentTag, action:'del'}, function(ret) {
        var index = self.tagList.findIndex(function(element){return element.key == self.currentTag;});
        if(index != -1)
            self.tagList.splice(index, 1);
      });
    },
    editTag() {
      var self = this;
      var params = {tag:self.currentTag, newtag:self.newTag, action:'edit', tagSortKey:self.tagSortKey, adminOnly:self.tagAdminOnly};
      self.tagDoPost(params, function(ret) {
        var element = self.tagList.find(function(element){return element.key == self.currentTag;});
        if(element) {
          element.key = self.newTag;
          element.adminOnly = self.tagAdminOnly;
          element.sort = self.tagSortKey;
        }
        if (self.checkedTagList[self.currentTag])
          self.checkedTagList[self.newTag] = true;
        self.newTag = '';
        self.tagAdminOnly = false;
      });
    },

    showTagMangeDialog(action, tag) {
      for (var key in this.showTagDialog) {
        this.showTagDialog[key] = false;
      }
      if (tag) {
        this.newTag = tag.key;
        this.tagSortKey = tag.sort;
        this.currentTag = tag.key;
        this.tagAdminOnly = tag.adminOnly;
      }
      this.showTagDialog[action] = true;
      if(action!=='add')
        this.showMask = true;
      this.action = action;
    },
    confirmTagManageDialog(action) {
      if (action === 'add') {
        this.addTagConfirm();
      } else if (action ==='del') {
        this.deleteTag();
        this.showTagDialog[action] = false;
      } else if (action === 'edit') {
        this.editTag();
        this.showTagDialog[action] = false;
      }
      this.showMask = false;
    },
    cancelTagManageDialog(action) {
      this.showTagDialog[action] = false;
      this.showMask = false;
    },
    getTopicList(cb) {
      var self = this;
      self.$http.get('/1.5/forum/topics').then(
        function (ret) {
          if (ret.data.ok) {
            self.topicList = ret.data.topics || [];
            if (cb)
              cb()
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        });
    },
    deletePostConfirm(action) {
      event.stopPropagation();
      this.showMask = true;
      var title = this.post.tl? this.post.tl.substring(0,10): '';
      var msg = this.$parent._(action + ' post?');
      if (action=='db_delete')
        msg= this.$parent._('delete post from db?');
      var placeholder = this.$parent._('Enter delete reason');
      return window.bus.$emit('show-confirm-dialog', {onConfirm:this.deletePost,id: this.post._id, action: action, msg:msg, placeholder: placeholder});
    },
    deletePost(des, id, action) {
      var self = this;
      if (!id) return;
      var params={id:id, city: self.post.city, prov: self.post.prov, gid: self.post.gid}, delFlag;
      if (action == 'delete') {
        params.del = 'delete';
        params.del_des = des;
        delFlag = true;
      } else if (action == 'recover') {
        params.del = 'recover';
        delFlag = false;
      } else if (action == 'db_delete') {
        params.del = 'db_delete';
        delFlag = true;
      }
      params.wpHosts = self.post.wpHosts;
      params.gid = this.post.gid;
      self.$http.post('/1.5/forum/edit', params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            self.post.del = delFlag;
            if (params.del == 'db_delete') {
              return window.rmCall(':ctx::cancel');
            }
          } else {
            return window.bus.$emit('flash-message', ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    deletePhoto(photo,type) {
      if (this.selectPhotoType == 'adTopPhoto') {
        this.adTopPhoto = this.post.adTopPhoto = '';
        return;
      }

      if (this.selectPhotoType == 'adBottomPhoto') {
        this.adBottomPhoto = this.post.adBottomPhoto = '';
        return;
      }

      var currIndex = this.post.photos.indexOf(photo);
      if (currIndex < 0) {
        return;
      }
      this.post.photos.splice(currIndex, 1);
    },
    previewPic(src,type) {
      this.selectPhotoType = type;
      // toggleModal('imgPreviewModal');
      // window.bus.$emit('open-img-preview', {});
      window.bus.$emit('img-preview', src);
      this.hideBackdrop = true;
      this.currentPic = "";
      this.currentPic = src;
      this.picRmConfirm = false;
    },
    sticky(sticky) {
      var self = this;
      self.$http.post('/1.5/forum/sticky/' + self.post._id, {type:'sticky', sticky:sticky, city: self.post.city, prov: self.post.prov, gid: self.post.gid}).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.sticky = sticky;
            self.post_sticky = sticky;
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          ajaxError(err);
        }
      );
    },
    unsetRank(){
      var self = this;
      self.rank = null;
      var radios = document.querySelectorAll('.radio-rank')
      for (var r = 0; r < radios.length; r++){
        radios[r].checked = false;
      }
    },
    checkInput(content){
      if (content && content.length){
        return replaceJSContent(content);
      }
      return content;
    }

  },
  computed: {
    computedTs: function() {
      var ts = new Date();
      if (this.post.mt)
        ts = new Date(this.post.mt);
      return ts.getFullYear() + '-' + (ts.getMonth() + 1) + '-' + ts.getDate() + ' ' + ts.getHours() + ":" + ts.getMinutes() + ":" + ts.getSeconds();
    },
    showGroup:function() {
      return this.dispVar.userGroups && this.dispVar.userGroups.length && ((this.isedit && vars.gid) || !this.isedit)
    }
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
    self.getPageData(self.datas, {}, true);
    window.bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.curCity = self.dispVar.userCity;
      window.bus.$emit('set-user-city', {city: self.dispVar.userCity});
    });
    self.getCategories(function() {
      self.categories.unshift({id:'none',sub:[],t:'None'})
    });

    if (!vars.gid) {
      self.getTopicList();
    }

    if(!vars.id) {
      self.getTagList();
    }
    if(vars.id) {
      this.initPost(vars.id, vars.gid);
      this.post.id = vars.id;
      this.isedit = true;
    }
    if(vars.src) {
      this.post.src = vars.src;
      this.isedit = true;
    }
    if(vars.tl) {
      this.post.tl = vars.tl;
      this.isedit = true;
    }
    bus.$on('set-city', function (d) {
      self.curCity = d.city;
      toggleModal('citySelectModal','close');
    });
    // bus.$on('select-img', function (data) {
    //   var picUrls = data.picUrls;
    //   toggleModal("imgSelectModal");
    //   if(self.selectPhotoType == 'adTopPhoto'){
    //     self.adTopPhoto = self.post.adTopPhoto = picUrls[0];
    //   }
    //   else if (self.selectPhotoType == 'adBottomPhoto') {
    //     self.adBottomPhoto =self.post.adBottomPhoto = picUrls[0];
    //   }
    //   else {
    //     for(var pic of picUrls) {
    //       if (self.post.photos.indexOf(pic) === -1) {
    //         self.post.photos.push(pic);
    //       }
    //     }
    //   }
    // });
    bus.$on('close-confirm-dialog', function(d) {
      self.showMask = false;
    });
    bus.$on('insert-form', function(form) {
      self.formName = self.post.formName = form.nm;
      self.post.formid = form._id;
    });
    bus.$on('select-user',function(d) {
      let agentArray = null;
      if(d.side == 'App'){
        if (d.type=='sponsor') {
          agentArray = self.sponsorAgents
        }
        if (d.type=='sponsorGrp') {
          agentArray = self.sponsorGroups
        }
      }
      let index = self.findAgentIndex(agentArray, d.user)
      if (index <0 && agentArray) {
        agentArray.push(d.user);
      }
    });
  },
  components: {
    CitySelectModal,
    FlashMessage,
    ConfirmWithMessage,
    imgPreviewModal,
    CreateNickname,
    AppFormSelectModal,
    // TrackLog,
    AgentSetting
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style>
.sub-wrapper{
  overflow: auto;
  padding-right: 10px;
  white-space: nowrap;
  background: #fff;
  border-bottom: 5px solid #f1f1f1;
  padding: 10px;
  width:100%;
}
.category-wrapper {
  background-color: #efefef;
  padding-top: 20px;
}
.category {
  background: white;
  padding: 10px;
  border-bottom: 1px solid #F0EEEE;
}
.category select {
   width: 200px;
   float: right;
}
.dropdown {
  z-index: 1000;
  background: #ffff;
  position: absolute;
  width: 60%;
  float: right;
  padding-right: 20px;
  position: absolute;
  right: 0;
  z-index: 1000;
  display: block;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0,0,0,.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
  box-shadow: 0 6px 12px rgba(0,0,0,.175);
}
.dropdown  li {
  display: block;
  padding: 5px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}
.subcategory {
  text-align: center;
  padding: 4px 20px;
  font-size: 12px;
  background-color: #f0f0f0;
  border-radius: 10px;
  color: #7c7c7c;
  margin-left: 5px;
  display: inline-block;
  position: relative;
}

.subcategory.active{
  background: #E03131;
  color: white;
}
.field-header span {
  padding-left: 10px;
  padding-right: 10px;
}
.tag-sort-input {
  width: 50px!important;
  height: 25px!important;
  margin-bottom: 0px!important;
  color: black;
  border:1px solid #ddd !important;
}
.select-topic.confirm-modal {
  top:100px;
  overflow: scroll;
  max-height: calc(100% - 150px);
}
.select-topic .field-name
{
  width: 60%;
}
[v-cloak] {
  display: none;
}
.title-input-div {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  padding-top: 10px;
  height: 30px;
  font-size: 17px;
}
.content-input-div {
  height: 80px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}
.modal textarea {
  resize: none;
  height:100%;
  margin-bottom:0;
  padding: 10px 5px;
}
.modal .bar-nav {
  padding-right:10px;
}
.bar-nav {
  padding-right:0px;
}
.bar .fa-back {
  font-size: 21px;
}
#forumEditContainer {
  position: relative;
  top:40px;
  width: 100%;
  margin-bottom: 60px;
}
.field-name {
  width: 20%;
  display: inline-block;
}
.field-name label {
  padding-top: 7px;
  margin-bottom: 15px;
  max-width: 100%;
  word-wrap: break-word;
  font-weight: bold;
  vertical-align: middle;
  display: inline-block;
}
.field-input {
  vertical-align: middle;
  width: 79%;
  display: inline-block;
  text-align: right;
}
.field-header {
  font-weight: bold;
  font-size: 17px;
  padding: 15px 10px 15px 10px;
  background-color: #efefef
}
.row {
  height: 57px;
  font-size: 17px;
  padding: 10px;
  overflow: hidden;
  background: #fff;
}
.video .row{
  border-bottom: 1px solid #F0EEEE;
}
.video .type > span{
  padding-left: 5px;
}
.video .type > input{
  margin-left: 20px;
}
.agent{
  height: auto;
}
.agent .row{
  height: auto;
  border-bottom: none;
}
.input-wrapper{
  display: inline-block;
  width: calc(100% - 150px);
}
.table-view-cell{
  border-bottom: 1px solid #F0EEEE;
}
.table-view-cell input {
  border: none;
  text-align: right;
  padding: 0
}
.table-view-cell.tag{
  padding: 11px 65px 11px 10px;
}
.table-view-cell.tag .field-name{
  width: 60%;
}
.tag-delete {
  padding-right: 10px;
  font-size: 20px;
}
.city {
  width: 50%;
  text-align: right;
  padding-top: 10px;
  padding-right: 5px;
}
.cityName{
  display: inline-block;
  text-align: right;
  max-width: calc(100% - 30px);
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.table-view-cell .icon-right{
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
}
.table-view{
  margin: 0px;
}
.table-view-cell .fa-check-square-o{
  color: #e03131;
  font-size: 20px;
}
.post-content {
  width: 100%;
  height: auto;
}
.table-view-cell .fa-square-o{
  padding-right: 2px;
  font-size: 20px;
}
.tag-input {
  width: 50%;
  border: 1px solid #F0EEEE;
}
.tag-input input {
  height: 30px;
  padding-top: 10px;
  padding-left: 10px;
  text-align: left;
}
.delete-tag-confirm {
  text-align: center;
}
.edit-tag-confirm {
  height:120px;
  padding-left: 10px;
  padding-top: 10px;
}
.edit-tag-confirm .newtag{
  width: 50%;
  margin-top:10px;
  color: black;
  padding:10px;
  height: 44px;
}
.icon {
  font-size: 24px;
}
.thumb-wrapper .selected {
  border: 3px solid #5cb85c;
}
.thumb-wrapper {
  height: 90px;
/*width: 90px;     */
  width: 33.33333%;
  display: inline-block;
  text-align: center;
/*padding-right: 10px;*/
}

#del-btn-wrapper{
  z-index: 40;
  position: absolute;
  background-color: rgba(0,0,0,0.5);
  text-align: center;
  vertical-align: middle;
  bottom: 5px;
  height: 50px;
  width: 50px;
  border-radius: 50%;
  left: 50%;
  /*margin-top: -25px;*/
  margin-left: -25px;
}

#del-btn-wrapper.active{
  position: absolute;
  background-color: rgba(0,0,0,0.5);
  text-align: center;
  vertical-align: middle;
  bottom: 5px;
  height: 40px;
  width: 150px;
  border-radius: 10px;
  left: 50%;
  /*margin-top: -20px;*/
  margin-left: -75px;
}
#gal-del-btn{
  color: white;
  font-size: 19px;
  margin-top: 10px;
  border: 1px none;
  background-color: transparent;
}
#gal-del-yes-btn{
  width: 55px;
  margin-top: 7px;
}
#gal-del-can-btn{
  width: 55px;
  margin-left: 10px;
  margin-top: 7px;
}

#gal-del-btn, #gal-del-yes-btn, #gal-del-can-btn{
  cursor: pointer;
}
.forum-photos {
  padding-left: 10px;
}

.forum-photos .image {
  background-size: 100% 100%;
  display: inline-block;
  width: 23%;
  margin: 1%;
  padding: 5px;
  height: 70px;
  vertical-align: top;
}
.forum-photos .new-img div {
  height: 100%;
  border: 3px dotted #ddd;
  width:100%
}
.forum-photos .new-img .icon{
  font-size: 20px;
  font-style: normal;
  text-align: center;
  margin-left: -9px;
  padding-top: 18px;
}
.lang-group .btn {
  width: 30px;
}
.post-recover{
  float: left;
  padding-left: 10px;
  padding-right: 10px;
}
.mask {
  background: rgba(0,0,0,0.8);
  opacity: 0.9;
  z-index: 18;
}
.publish {
  color: white;
  font-size: 17px;
  position: relative;
  z-index: 20;
  padding-top: 15px;
  padding-right: 10px;
  font-weight: bold;
  border:none;
  background-color: #E03131
}
.post-content textarea {
  border: none;
  padding:0;
  margin-bottom:0px
}
.web {
  top:0 !important;
}
.bar-footer {
  padding-right: 0px !important
}

.bar-footer .publish-btn {
  background-color: #E03131;
  color: white;
  width: 50%;
  height: 100%;
  text-align: center;
  padding-left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
}
.field-header input[type="radio"]{
  margin: 0 10px;
}
</style>
