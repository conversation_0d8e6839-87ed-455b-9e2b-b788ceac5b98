.vidRecord.noPic{
  margin-top: 22px;
  margin-left: 36px;
  border: 2px solid #d6d6d6;
  position: absolute;
}
.vidRecord{
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  text-align: center;
  font-size: 20px;
  width: 44px;
  height: 44px;
  border: 2px solid white;
  color: white;
  padding-top: 10px;
  float: right;
  margin-top: -67px;
  margin-right: 38px;
  display: inline;
}
.vidRecord .fa{

}
.vidLive.noPic{
  margin-top: 70px;
  /* margin-left: 44px; */
  right: 10px;
  position: absolute;
}
.vidLive{
  font-size: 11px;
  font-weight: bold;
  color: white;
  border-radius: 3px;
  padding: 0px 3px;
  line-height: 14px;
  float: right;
  display: inline;
  margin-top: -20px;
  margin-right: 7px;
  height: 14px;
  width: 69px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  white-space: nowrap;
}
.vidLive1{
  width: 36px;
  background: #e03131;
}
.vidLive2{
  background: black;
}
.noCity {
  color: #e03131;
}
.post-title .txt {
  font-weight: bold;
  color:#191919
}
.post-summary .realtor-only {
  color: white!important;
  font-size: 7px!important;
  background-color: #E03131;
  padding: 1px 3px;
  border-radius: 2px;
}
.topictitle {
  font-size: 19px!important;
}
.post-top-div .edit {
  position: absolute;
  background: white;
  margin: 5px;
}
.post-top-div .edit span{
  padding: 5px;
  font-size: 10px;
}
.post-city span {
  position: absolute;
  right: 31%;;
  margin-top: -30px;
  background: white;
  font-size: 10px;
  line-height: 15px;
}
.red-dot-forum {
  color:red;
  padding-right: 3px;
  font-size: 10px;
}
.full-width {
  width: 100% !important;
}
.forum-summary-card{
  background: white;
  /*height: 123px;*/
  padding:10px;
  border-bottom: 5px solid #F0EEEE;
}
.post-title span {
  display:inline;
  vertical-align:middle;
  text-align:center;
}
.post-title {
  overflow: hidden;
  font-size: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  height: 75px;
  margin-bottom: 10px;
  padding: 5px 10px 0px 5px;
  line-height: 22px;
}
.post-name {
  white-space: nowrap;
  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  float: left;
  padding-right: 5px;
  color:#a9a3a3
}
.post-ts {
  color:#a9a3a3;
  line-height: 16px;
  padding-left: 5px;
}
.post-summary {
  width: calc(100% - 120px);
  float: left;
}
.post-top-img {
  width: 100%;
  /* max-height: 180px; */
  margin-bottom: -5px;
}
.forum-summary-card .post-img {
  float: left;
  width: 120px;
  height: 90px;
  background-size: 100% 100%;
}
.post-img .no-img {
  background-color: #fff;
  border: 1px solid #eaebec;
  font-size: 15px;
  text-align: center;
  vertical-align: middle;
  display: grid;
  vertical-align: middle;
  width: 100%;
  font-size: 50px;
  line-height: 90px;
  color: #d2d5d8;
  border-radius: 5px;
}
.post-comments {
  font-size: 10px;
  color:#a9a3a3;
  padding: 0 5px;
  white-space: nowrap;
  position: relative;
}
.deleted {
  text-decoration: line-through;
}
.greybg {
  background-color: #fdfcfc;
}
.post-bottom span {
  padding-left: 2px;
  display:inline-block;
  /*vertical-align:middle;*/
  text-align:center;
}
p::first-line {
  padding-left: 50px;
}
.summaryWeb .red-button {
  font-size: 14px !important;
}
.summaryWeb .post-comments {
  font-size: 12px !important;
}
.summaryWeb .post-comments {
  font-size: 12px !important;
}
.summaryWeb .post-summary  {
  width: calc(100% - 121px)!important;
}
.summaryWeb .post-img  {
  width: 120px !important;
  height: 95px !important
}
.summaryWeb .post-img .no-img {
  line-height: 90px;
}
.summaryWeb .post-top-img {
  margin-top: 0px;
}
.red-button {
  color: #e03131;
  border: 0.5px solid #f00;
  font-style: normal;
  padding: 1px 3px;
  border-radius: 2px;
  font-size: 12px;
  margin-right: 5px;
  min-width: 25px;
  text-align: center;
}
.blue {
  color: #00b2ee!important;
  border-color: #00b2ee!important;
}
.reportForumIcon {
  position: absolute;
  top: 50%;
  right: 10px;
  @include css3('transform','translateY(-50%)');
}
.icon.icon-close.reportForumIcon {
  font-size: 10px;
  color: #a9a3a3;
  background-color: #f5f5f5;
  padding: 1px 3px;
  border-radius: 2px;
}
.post-top-text {
  color: white;
  width: auto;
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
  padding: 0px 4px 0px;
  /* margin-top: -28px; */
  height: 19px;
  font-size: 12px;
  background: rgba(94, 93, 93,0.7);
}
.post-top-text span {
  padding-left: 2px;
}