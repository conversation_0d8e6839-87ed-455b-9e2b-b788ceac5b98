# 需求 [topListing_token]

房源置顶新需求:inReal的用户，可以用token置换置顶1个月。只能置顶Avion Realty Inc.（定义统一常量里面）的房源。初期设置出租 50，出售 150 token。

## 需求提出人:    Fred
## 修改人：       ZhangMan

## 提出日期:      2025-07-03

## 解决办法

1. 在现有的置顶房源按钮展示权限中增加inreal和房源rltr: “AVION REALTY INC."的检查，如果这个条件满足,该按钮也可展示出现；
2. 点击置顶房源按钮，进入房源置顶页面，如果只满足inreal和房源rltr: “AVION REALTY INC."的条件则不展示现有的支付价格置顶界面，只展示token兑换；如果两个条件都满足，就都展示；如果只满足原先的条件，则只展示支付价格置顶界面；
3. 点击置换按钮，提示用户扣除多少token，确认后扣除用户token，调用原来的/1.5/prop/topup/charge；

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-10

## online-step

1. 先在data数据库的sysdata表中systemTokenKeys里增加两个tokenKey，mongo语句：
db.getCollection("sysdata").update({_id:'systemTokenKeys'},{$push:{item:{$each:[{"nm":"Top selling properties for sale","key":"topPropSale","t" : NumberInt(10000)},{"nm":"Top selling properties for rent","key":"topPropRent","t" : NumberInt(5000)}]}}})

2. 重启服务
